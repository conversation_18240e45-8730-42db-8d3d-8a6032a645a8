<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Entities build="b68" majorVersion="8" minorVersion="3" modelPersistenceProviderPackage="PostgresPersistenceProviderPackage" revision="9" schemaVersion="1032" universal="">
  <Things>
    <Thing description="配置二三级表" documentationContent="" effectiveThingPackage="ConfiguredThing" enabled="true" homeMashup="" identifier="" lastModifiedDate="2025-06-09T13:54:54.410+08:00" name="Thing.Fn.SecondTable" projectName="DataPackageProject" published="false" tags="" thingTemplate="GenericThing" valueStream="">
      <avatar/>
      <DesignTimePermissions>
        <Create/>
        <Read/>
        <Update/>
        <Delete/>
        <Metadata/>
      </DesignTimePermissions>
      <RunTimePermissions/>
      <VisibilityPermissions>
        <Visibility/>
      </VisibilityPermissions>
      <ConfigurationTableDefinitions/>
      <ConfigurationTables/>
      <ThingShape>
        <PropertyDefinitions/>
        <ServiceDefinitions>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddParam">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="PARAM_NAME" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="MES_NAME" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="THREE_AREA" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="SECOND_AREA" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="TABLE_ID" description="" ordinal="5"/>
              <FieldDefinition baseType="STRING" name="FORMAT" description="" ordinal="6"/>
              <FieldDefinition baseType="STRING" name="WIDTH" description="" ordinal="7"/>
              <FieldDefinition baseType="INTEGER" name="IS_SORT" description="" ordinal="8" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="IS_REMOVE_REPEAT" description="" ordinal="9" aspect.defaultValue="0"/>
              <FieldDefinition baseType="STRING" name="tableName" description="" ordinal="10"/>
              <FieldDefinition baseType="INTEGER" name="RELATION_PARAM" description="" ordinal="11"/>
              <FieldDefinition baseType="INTEGER" name="VALUE_TYPE" description="" ordinal="12"/>
              <FieldDefinition baseType="INTEGER" name="IS_BASE" description="" ordinal="13" aspect.defaultValue="0"/>
              <FieldDefinition baseType="STRING" name="INTERFACE_NAME" description="" ordinal="14" aspect.defaultValue=" "/>
              <FieldDefinition baseType="INTEGER" name="IS_INDEX" description="" ordinal="15" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="IS_QUERY" description="" ordinal="16" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="IS_SHOW_IN_360" description="" ordinal="17" aspect.defaultValue="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="增加一条质量影像记录 wanghq 2021年12月1日14:47:39" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddQualityPhoto">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="JSON" name="data" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2021年12月1日14:47:39" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddQualityPhotos">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="JSON" name="datas" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="添加质量策划表  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddQualityPlan">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="filepath" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="filename" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="data" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="5"/>
              <FieldDefinition baseType="INTEGER" name="type" description="" ordinal="6"/>
              <FieldDefinition baseType="STRING" name="originalData" description="" ordinal="7"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="添加质量报告模板文件  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddQualityReportTpl">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="nodeName" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="fileName" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="filePath" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="fileFormat" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="createTime" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="质量数据确认表 增加一条签名信息 wanghq 2021年12月2日18:18:18" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddQualitySign">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="qualityPlanId" description="" ordinal="0"/>
              <FieldDefinition baseType="INTEGER" name="type" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="img" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="3"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddTable">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="TREE_NAME" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="TYPE" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="MES_INTERFACE" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="SECOND_DATA_ROWNUM" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="THREE_DATA_ROWNUM" description="" ordinal="5"/>
              <FieldDefinition baseType="INTEGER" name="PLAN_START_COLINDEX" description="" ordinal="6" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="PLAN_END_COLINDEX" description="" ordinal="7" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="PHOTO_START_COLINDEX" description="" ordinal="8" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="PHOTO_END_COLINDEX" description="" ordinal="9" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="IS_QUERY" description="" ordinal="10" aspect.defaultValue="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddTableData">
            <ResultType baseType="INTEGER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="TABLE_CONFIG_ID" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="TREE_ID" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="FILEPATH" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="FILENAME" description="" ordinal="4"/>
              <FieldDefinition baseType="JSON" name="data" description="" ordinal="5"/>
              <FieldDefinition baseType="STRING" name="createTime" description="" ordinal="6"/>
              <FieldDefinition baseType="STRING" name="productId" description="" ordinal="7"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="AddTestParam">
            <ResultType baseType="NOTHING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="批量同步过程节点的所有二级表数据    wanghq 2025年5月27日10:36:15" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="BatchSyncAllTables">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="过程节点树ID" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="取消行内质量数据确认 datetime 2023年7月13日09:26:43" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CancelDataConfirm">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="onlyValue" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="paramId" description="" ordinal="1"/>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="optUser" description="" ordinal="3"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="4"/>
              <FieldDefinition baseType="INTEGER" name="type" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="changeData">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="changeData_OSR">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="changeData_热控喷涂">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="changeData_热管">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="清除现有的二级表中的空行 wanghq 2023年5月4日17:37:40" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ClearEmptyRow">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="清除配置表相关数据  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ClearTable">
            <ResultType baseType="NOTHING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2022年7月28日15:28:37" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CompareValue">
            <ResultType baseType="BOOLEAN" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="designStringValue" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="actualStringValue" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="确认质量数据 消除超差数据红色报警 wanghq 2023年12月11日15:14:03" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ConfirmTableData">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tableName" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="ids" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="fullname" description="" ordinal="2"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="3"/>
              <FieldDefinition baseType="NUMBER" name="tableConfigId" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="username" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ConvertTableHtml">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="JSON" name="datas" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CopyTable">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="TREE_NAME" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="TYPE" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="MES_INTERFACE" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="SECOND_DATA_ROWNUM" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="THREE_FILEPATH" description="" ordinal="5"/>
              <FieldDefinition baseType="STRING" name="SECOND_FILEPATH" description="" ordinal="6"/>
              <FieldDefinition baseType="INTEGER" name="ID" description="" ordinal="7"/>
              <FieldDefinition baseType="STRING" name="TABLE_NAME" description="" ordinal="8"/>
              <FieldDefinition baseType="STRING" name="THREE_DATA_ROWNUM" description="" ordinal="9"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="所有的二级表 和清单表 新增 product_tree_ids  wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="DealProductId">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="删除下载列表中已经下载的文件 datetime 2023年8月7日21:45:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="DeleteDownloadFile">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="DeleteParam">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="ids" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="质量影像记录查看照片时候删除操作 wanghq 2021年12月9日14:54:10" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="DeletePhoto">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="id" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="photoPath" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="DeleteTable">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="ID" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="删除质量数据 wanghq 2022年7月18日15:20:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="DeleteTableData">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="tableName" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="ids" description="" ordinal="2"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="3"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="生成文件完成 datetime 2023年8月7日17:14:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GenerateFileComplete">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="filePath" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="fileName" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="fileSize" description="" ordinal="2"/>
              <FieldDefinition baseType="NUMBER" name="downloadId" description="" ordinal="3"/>
              <FieldDefinition baseType="NUMBER" name="isComplete" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="msg" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="生成质量报告  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GenerateQualityReportByTreeId">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="treeId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="getChildNodeInTree">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="pid" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="通过表名获取列的最新序列号  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetNewParamSeq">
            <ResultType baseType="INTEGER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tableName" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="根据树节点ID获取质量数据类型，二三级表类型+excel导入类型+自动采集的质量数据类型  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetProductTypeByTreeId">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2021年11月25日10:30:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetQualityPhotoTypeByTreeId">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="获取二级表的表头  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetSecondTableHeader">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="id" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="获取查询二级表数据的sql  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetTableDataSql">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="productTreeId" description="" ordinal="0" aspect.defaultValue="-1.0"/>
              <FieldDefinition baseType="STRING" name="processTreeId" description="" ordinal="1" aspect.defaultValue="-1"/>
              <FieldDefinition baseType="INTEGER" name="table_config_id" description="" ordinal="2"/>
              <FieldDefinition baseType="JSON" name="query" description="" ordinal="3" aspect.PP-264deacd-0679-49c8-856b-19f8662535d0="{&quot;defaultValue1665197764296&quot;:&quot;PP-264deacd-0679-49c8-856b-19f8662535d0-defaultValue1665197764296&quot;}" aspect.defaultValue="{&quot;params&quot;:[]}"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="判断字符串是否只有大写字母  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="isCapital">
            <ResultType baseType="BOOLEAN" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="str" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2021年12月28日10:11:02" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="manualSyncMes">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="type" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="tableId" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="endY" description="" ordinal="3"/>
              <FieldDefinition baseType="INTEGER" name="tableType" description="" ordinal="4"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="阿拉伯数字转中文数字  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="NumToChinese">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="num" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ParseQualityPhotoPlan">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="data" description="" ordinal="0"/>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="1"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="2"/>
              <FieldDefinition baseType="INTEGER" name="type" description="" ordinal="3" aspect.defaultValue="2"/>
              <FieldDefinition baseType="BOOLEAN" name="init" description="" ordinal="4" aspect.defaultValue="false"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ParseQualityPlan">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="data" description="" ordinal="0"/>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="1"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="2"/>
              <FieldDefinition baseType="BOOLEAN" name="init" description="" ordinal="3" aspect.defaultValue="false"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="4"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="行内质量数据确认 wanghq 2021年11月23日15:32:49" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QualityDataConfirm">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="onlyValue" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="paramId" description="" ordinal="1"/>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="paramName" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="confirmer" description="" ordinal="4"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="5"/>
              <FieldDefinition baseType="INTEGER" name="type" description="" ordinal="6"/>
              <FieldDefinition baseType="STRING" name="paramValue" description="" ordinal="7" aspect.defaultValue=" "/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询影像记录策划表中的所有照片 wanghq 2023年2月16日11:03:21" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryAllPhoto">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryAllTable">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询下载列表 datetime 2023年8月7日21:05:09" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryDownloadTable">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="tableConfigId" description="" ordinal="1"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="2"/>
              <FieldDefinition baseType="NUMBER" name="type_" description="" ordinal="3"/>
              <FieldDefinition baseType="INTEGER" name="page" description="" ordinal="4"/>
              <FieldDefinition baseType="INTEGER" name="limit" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryMesTable">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableId" description="" ordinal="0" aspect.defaultValue="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryParams">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tree_name" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryParamsById">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tableId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询过程节点的质量数据确认表  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryProcessQualityData">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询过程节点的质量数据确认表  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryProcessQualityPhotoData">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询质量影像记录汇总表 wanghq 2021年12月28日14:24:05" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryQualityPhotoSummary">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="tree_id" description="" ordinal="0"/>
              <FieldDefinition baseType="INTEGER" name="table_config_id" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询质量报告模板  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryQualityReportTpl">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="nodeName" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询质量数据签名 wanghq 2021年12月2日18:44:35" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryQualitySign">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="qualityPlanId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询质量数据汇总表 wanghq 2021年12月28日14:24:05" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryQualitySummary">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="tree_id" description="" ordinal="0"/>
              <FieldDefinition baseType="INTEGER" name="table_config_id" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="username" description="" ordinal="2"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="用于全景图中查询单机信息 wanghq 2023年10月23日14:06:57" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryStandAlone">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="info" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="queryUser" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="用于全景图中查询单机信息 wanghq 2025年6月9日14:06:57" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryStandAloneByModel">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="modelId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTable">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tree_name" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTableById">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tableId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTableByNodename">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="nodename" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询电缆网元器件数据 合格证相同的产品的实际装机数量要累加 wanghq 2022年4月22日15:59:25" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTableComponent">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="processTreeId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="productTreeId" description="" ordinal="1"/>
              <FieldDefinition baseType="INTEGER" name="table_config_id" description="" ordinal="2"/>
              <FieldDefinition baseType="JSON" name="query" description="" ordinal="3"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTableData">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="processTreeId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="table_config_id" description="" ordinal="1"/>
              <FieldDefinition baseType="INTEGER" name="productTreeId" description="" ordinal="2" aspect.defaultValue="-1"/>
              <FieldDefinition baseType="INTEGER" name="dlw_is_all" description="" ordinal="3" aspect.defaultValue="2"/>
              <FieldDefinition baseType="JSON" name="query" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="table_config_name" description="" ordinal="5" aspect.defaultValue="undefined"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTableDataCount">
            <ResultType baseType="INTEGER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="processTreeId" description="" ordinal="0" aspect.defaultValue="-1"/>
              <FieldDefinition baseType="INTEGER" name="table_config_id" description="" ordinal="1"/>
              <FieldDefinition baseType="NUMBER" name="productTreeId" description="" ordinal="2"/>
              <FieldDefinition baseType="JSON" name="query" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="table_config_name" description="" ordinal="4" aspect.defaultValue="undefined"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="分页查询二级表数据  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTableDataPage">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="processTreeId" description="" ordinal="0" aspect.defaultValue="-1"/>
              <FieldDefinition baseType="INTEGER" name="table_config_id" description="" ordinal="1"/>
              <FieldDefinition baseType="NUMBER" name="productTreeId" description="" ordinal="2"/>
              <FieldDefinition baseType="INTEGER" name="pageNumber" description="" ordinal="3"/>
              <FieldDefinition baseType="INTEGER" name="pageSize" description="" ordinal="4"/>
              <FieldDefinition baseType="JSON" name="query" description="" ordinal="5"/>
              <FieldDefinition baseType="STRING" name="table_config_name" description="" ordinal="6" aspect.defaultValue="undefined"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询表格的排序列和去重列  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTableSortCol">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="查询单元格的照片信息 wanghq 2022年12月6日23:06:38" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTdPhoto">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="onlyValue" description="" ordinal="2"/>
              <FieldDefinition baseType="NUMBER" name="paramId" description="" ordinal="3"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTemplateTree">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTreeNodeByName">
            <ResultType baseType="INFOTABLE" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="id" description="" ordinal="0" aspect.isRequired="true"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="通过table_config的id查询关联的树节点  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTreesByTableId">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryType4TableData">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tree_id" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="table_config_id" description="" ordinal="1"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="重新关联质量数据  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ReassociationData">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="dataIds" description="" ordinal="1"/>
              <FieldDefinition baseType="INTEGER" name="tableId" description="" ordinal="2"/>
              <FieldDefinition baseType="NUMBER" name="newTreeId" description="" ordinal="3"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="记录下载在下载列表中的文件 datetime 2023年8月7日21:02:26" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="RecordDownloadFile">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="downloadId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="将质量数据关联到产品结构树节点上  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="RelationBomTree">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="tableId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="bomTreeId" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="dataIds" description="" ordinal="2"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="RelationTable">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="ids" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="nodename" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="2"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="true" category="" description="发起生成文件请求 datetime 2023年8月7日16:56:24" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ReqGenerateFile">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="tableConfigId" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="1"/>
              <FieldDefinition baseType="NUMBER" name="exportType" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="3"/>
              <FieldDefinition baseType="NUMBER" name="type_" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="fileName" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="请求试验管控系统的接口  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="reqTestControl">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="type" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="reqUrl" description="" ordinal="2"/>
              <FieldDefinition baseType="INTEGER" name="tableId" description="" ordinal="3"/>
              <FieldDefinition baseType="INTEGER" name="endY" description="" ordinal="4"/>
              <FieldDefinition baseType="INTEGER" name="tableType" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="请求单个试验管控系统的表格 wanghq 2022年8月31日13:16:55" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ReqTestTable">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="reqUrl" description="" ordinal="0"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="interfaceName" description="" ordinal="2"/>
              <FieldDefinition baseType="NUMBER" name="tableId" description="" ordinal="3"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="字符串转成可插入的clob类型的sql  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="StrToClobSql">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="str" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="SyncMesData">
            <ResultType baseType="NOTHING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="同步试验管控系统的确认表 wanghq 2022年8月31日10:21:26" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="SyncTestConfirmTable">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="test">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="test2">
            <ResultType baseType="XML" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="testclob">
            <ResultType baseType="NOTHING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="clob" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="testclob2">
            <ResultType baseType="NOTHING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="clob" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="testMes">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="type" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="reqUrl" description="" ordinal="2"/>
              <FieldDefinition baseType="INTEGER" name="tableId" description="" ordinal="3"/>
              <FieldDefinition baseType="INTEGER" name="endY" description="" ordinal="4"/>
              <FieldDefinition baseType="INTEGER" name="tableType" description="" ordinal="5"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="TestXml">
            <ResultType baseType="XML" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="TestXml1">
            <ResultType baseType="XML" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="transbase10">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="str" description="" ordinal="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="更新历史数据  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateHistoryData">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="更新历史的表格配置表  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateHistoryTableConfig">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="更新表结构以及同步历史数据  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateHistoyrTableAndData">
            <ResultType baseType="STRING" description="" name="result" ordinal="0"/>
            <ParameterDefinitions/>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateParam">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="PARAM_NAME" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="MES_NAME" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="THREE_AREA" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="modifier" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="ID" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="SECOND_AREA" description="" ordinal="5"/>
              <FieldDefinition baseType="STRING" name="FORMAT" description="" ordinal="6"/>
              <FieldDefinition baseType="STRING" name="WIDTH" description="" ordinal="7"/>
              <FieldDefinition baseType="INTEGER" name="IS_REMOVE_REPEAT" description="" ordinal="8"/>
              <FieldDefinition baseType="INTEGER" name="IS_SORT" description="" ordinal="9"/>
              <FieldDefinition baseType="INTEGER" name="TABLE_ID" description="" ordinal="10"/>
              <FieldDefinition baseType="INTEGER" name="VALUE_TYPE" description="" ordinal="11"/>
              <FieldDefinition baseType="INTEGER" name="RELATION_PARAM" description="" ordinal="12"/>
              <FieldDefinition baseType="INTEGER" name="IS_BASE" description="" ordinal="13"/>
              <FieldDefinition baseType="STRING" name="INTERFACE_NAME" description="" ordinal="14" aspect.defaultValue=" "/>
              <FieldDefinition baseType="INTEGER" name="IS_INDEX" description="" ordinal="15" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="IS_QUERY" description="" ordinal="16" aspect.defaultValue="0"/>
              <FieldDefinition baseType="INTEGER" name="IS_SHOW_IN_360" description="" ordinal="17" aspect.defaultValue="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="行内质量数据状态更新 wanghq 2021年12月3日16:51:01" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateQualityDataStatus">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="onlyValue" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="paramId" description="" ordinal="1"/>
              <FieldDefinition baseType="INTEGER" name="tableConfigId" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="paramName" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="creator" description="" ordinal="4"/>
              <FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="5"/>
              <FieldDefinition baseType="INTEGER" name="type" description="" ordinal="6"/>
              <FieldDefinition baseType="STRING" name="paramValue" description="" ordinal="7"/>
              <FieldDefinition baseType="STRING" name="status" description="" ordinal="8"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateTable">
            <ResultType baseType="NUMBER" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="TREE_NAME" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="TYPE" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="MES_INTERFACE" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="modifier" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="ID" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="SECOND_DATA_ROWNUM" description="" ordinal="5"/>
              <FieldDefinition baseType="STRING" name="THREE_DATA_ROWNUM" description="" ordinal="6"/>
              <FieldDefinition baseType="INTEGER" name="PLAN_START_COLINDEX" description="" ordinal="7"/>
              <FieldDefinition baseType="INTEGER" name="PLAN_END_COLINDEX" description="" ordinal="8"/>
              <FieldDefinition baseType="INTEGER" name="PHOTO_START_COLINDEX" description="" ordinal="9"/>
              <FieldDefinition baseType="INTEGER" name="PHOTO_END_COLINDEX" description="" ordinal="10"/>
              <FieldDefinition baseType="INTEGER" name="IS_QUERY" description="" ordinal="11" aspect.defaultValue="0"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="更新二级表、三级表模板位置  wanghq 2025年5月22日9:55:53" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateTableTplFile">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="INTEGER" name="type" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="filepath" description="" ordinal="1"/>
              <FieldDefinition baseType="STRING" name="modifier" description="" ordinal="2"/>
              <FieldDefinition baseType="INTEGER" name="id" description="" ordinal="3"/>
            </ParameterDefinitions>
          </ServiceDefinition>
          <ServiceDefinition aspect.isAsync="false" category="" description="上传二级表中的图片 datetime 2023年12月19日16:27:47" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UploadTableImage">
            <ResultType baseType="JSON" description="" name="result" ordinal="0"/>
            <ParameterDefinitions>
              <FieldDefinition baseType="STRING" name="tableName" description="" ordinal="0"/>
              <FieldDefinition baseType="STRING" name="colName" description="" ordinal="1"/>
              <FieldDefinition baseType="NUMBER" name="dataId" description="" ordinal="2"/>
              <FieldDefinition baseType="STRING" name="filePath" description="" ordinal="3"/>
              <FieldDefinition baseType="STRING" name="fileFormat" description="" ordinal="4"/>
              <FieldDefinition baseType="STRING" name="fileName" description="" ordinal="5"/>
              <FieldDefinition baseType="STRING" name="username" description="" ordinal="6"/>
            </ParameterDefinitions>
          </ServiceDefinition>
        </ServiceDefinitions>
        <EventDefinitions/>
        <ServiceMappings/>
        <ServiceImplementations>
          <ServiceImplementation description="" handlerName="Script" name="AddParam">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddParam
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {STRING}    PARAM_NAME    
 * @param    {STRING}    MES_NAME    
 * @param    {STRING}    THREE_AREA    
 * @param    {STRING}    creator    
 * @param    {STRING}    SECOND_AREA    
 * @param    {STRING}    TABLE_ID    
 * @param    {STRING}    FORMAT    
 * @param    {STRING}    WIDTH    
 * @param    {INTEGER}    IS_SORT        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    IS_REMOVE_REPEAT        {"aspect.defaultValue":"0"}
 * @param    {STRING}    tableName    
 * @param    {INTEGER}    RELATION_PARAM    
 * @param    {INTEGER}    VALUE_TYPE    
 * @param    {INTEGER}    IS_BASE        {"aspect.defaultValue":"0"}
 * @param    {STRING}    INTERFACE_NAME        {"aspect.defaultValue":" "}
 * @param    {INTEGER}    IS_INDEX        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    IS_QUERY        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    IS_SHOW_IN_360        {"aspect.defaultValue":"0"}
 *
 * @returns    {NUMBER}
 */
var paramIndex = me.GetNewParamSeq({
    tableName: tableName
});
var columnName = "V" + paramIndex;
var colunmType = "VARCHAR2(2000)";
//影像记录或者跟踪卡链接
if (FORMAT == "3" || FORMAT == "4") {
    colunmType = "CLOB";
}
var alterSql = "alter table " + tableName + " add " + columnName + " " + colunmType;
Things["Thing.DB.Oracle"].RunCommand({
    sql: alterSql /* STRING */
});

var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
var newId = Things["Thing.DB.Oracle"].RunQuery({
    sql: "select PARAM_CONFIG_SEQ.nextval val from dual"
}).rows[0]["VAL"];

//现将接口属性置空再设置 避免一个接口属性有两个值
if (INTERFACE_NAME != "" &amp;&amp; INTERFACE_NAME != " ") {
    Things['Thing.DB.Oracle'].RunCommand({ sql: "update PARAM_CONFIG set INTERFACE_NAME='' where TABLE_ID=" + TABLE_ID + "  and INTERFACE_NAME='" + INTERFACE_NAME + "'" });
}

var sql = "insert into PARAM_CONFIG (ID, PARAM_NAME, MES_NAME, THREE_AREA, SECOND_AREA,WIDTH,FORMAT,TABLE_ID," +
    "IS_SORT,IS_REMOVE_REPEAT,IS_BASE,IS_INDEX,IS_QUERY,IS_SHOW_IN_360,INTERFACE_NAME,MODIFIER, CREATE_TIME,RELATION_PARAM, VALUE_TYPE,TABLE_NAME,COL_NAME)" +
    "values (" + newId + ",'" + PARAM_NAME + "', '" + MES_NAME + "', '" + THREE_AREA + "', '" + SECOND_AREA + "', '" + WIDTH + "', '" + FORMAT + "', '" + TABLE_ID +
    "', '" + IS_SORT + "', '" + IS_REMOVE_REPEAT + "', '" + IS_BASE + "', '" + IS_INDEX + "', '" + IS_QUERY + "','" + IS_SHOW_IN_360 + "', '" + INTERFACE_NAME + "', '" + creator + "', '" + now + "', '" + RELATION_PARAM + "', '" + VALUE_TYPE + "', '" + tableName + "', '" + columnName + "')";
result = Things["Thing.DB.Oracle"].RunCommand({
    sql: sql /* STRING */
});
if (VALUE_TYPE != 0) {
    var valueType = VALUE_TYPE == 1 ? 2 : 1;
    //更新相关属性的值
    Things["Thing.DB.Oracle"].RunCommand({
        sql: "update PARAM_CONFIG set VALUE_TYPE=" + valueType + ",RELATION_PARAM=" + newId + " where ID=" + RELATION_PARAM
    });
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddQualityPhoto">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddQualityPhoto
 * @description   增加一条质量影像记录 wanghq 2021年12月1日14:47:39
 * @implementation    {Script}
 *
 * @param    {JSON}    data    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};
try {
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    var nextId = Things['Thing.DB.Oracle'].RunQuery({ sql: "select QUALITY_PHOTO_SEQ.NEXTVAL as nextid from dual" }).rows[0].NEXTID;
    var sql = "insert into QUALITY_SINGLE_PHOTO (ID, \
        TABLE_CONFIG_ID, \
        TREE_ID, \
        ONLY_VALUES, \
        PHOTO_NAME, \
        PHOTO_PATH, \
        DOWNLOAD_URL, \
        PREVIEW_URL, \
        PHOTO_FORMAT, \
        PHOTO_NUMBER, \
        CREATOR, \
        CREATE_TIME, \
        PARAM_ID, \
        PHOTO_SIZE\
        )values ("+ nextId + ", \
            " + data.tableConfigId + ", \
            " + data.treeId + ", \
            '" + (data.onlyValue || "") + "', \
            '" + (data.photoName || "") + "', \
            '" + (data.photoPath || "") + "', \
            '" + (data.downloadUrl || "") + "', \
            '" + (data.previewUrl || "") + "', \
            '" + (data.photoFormat || "") + "', \
            '" + (data.photoNumber || "") + "', \
            '" + (data.creator || "adm") + "', \
            '" + nowTime + "', \
            '" + (data.paramId || "") + "', \
            '" + (data.photoSize || "") + "' \
            )";
    Things["Thing.DB.Oracle"].RunCommand({ sql: sql });
    res.data = nextId;
} catch (error) {
    var msg = "添加质量影像记录失败，原因：" + error;
    res.success = false;
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddQualityPhotos">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddQualityPhotos
 * @description   wanghq 2021年12月1日14:47:39
 * @implementation    {Script}
 *
 * @param    {JSON}    datas    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};
try {
    for (var i = 0; i &lt; datas.array.length; i++) {
        me.AddQualityPhoto({
            data: datas.array[i]
        });
    }
    res.msg = "添加多条质量影像记录成功";
} catch (error) {
    var msg = "添加多条质量影像记录失败，原因：" + error;
    logger.error(msg);
    res.success = false;
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddQualityPlan">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddQualityPlan
 * @description   添加质量策划表  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {INTEGER}    tableConfigId    
 * @param    {STRING}    filepath    
 * @param    {STRING}    filename    
 * @param    {STRING}    data    
 * @param    {STRING}    creator    
 * @param    {INTEGER}    type    
 * @param    {STRING}    originalData    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};
try {
    var selSql = "select * from QUALITY_PLAN where treeid = " + treeId + " and TABLE_CONFIG_ID=" + tableConfigId + " and type=" + type;
    var rs1 = Things["Thing.DB.Oracle"].RunQuery({ sql: selSql });
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    if (rs1.rows.length &gt; 0) {
        //说明之前已经上传过了
        var updateSql = "update QUALITY_PLAN set filepath='" + filepath + "',filename='" + filename + "',update_time='" + nowTime + "',UPDATER='" + creator + "',data=" + data + " where id=" + rs1.rows[0].ID;
        Things["Thing.DB.Oracle"].RunCommand({ sql: updateSql });
        //同时清空该表的确认信息 （不需要清空确认信息）
        // var deleteConfirmSql = "delete from QUALITY_CONFIRM where treeid=" + treeId + " and table_config_id=" + tableConfigId;
        // Things["Thing.DB.Oracle"].RunCommand({ sql: deleteConfirmSql });
        res.msg = "更新质量数据策划表成功";
    } else {
        var addSql = "insert into QUALITY_PLAN (ID, TREEID, TABLE_CONFIG_ID, TYPE, FILEPATH, FILENAME,DATA, CREATE_TIME, CREATOR )values (QUALITY_PLAN_SEQ.NEXTVAL," + treeId + "," + tableConfigId + "," + type + ",'" + filepath + "','" + filename + "'," + data + ",'" + nowTime + "','" + creator + "')";
        Things["Thing.DB.Oracle"].RunCommand({ sql: addSql });
        res.msg = "添加质量数据策划表成功";
    }
    var rs2;
    if (type == 1) {
        rs2 = me.ParseQualityPlan({
            data: originalData,
            tableConfigId: tableConfigId,
            treeId: treeId,
            init: true,
            creator: creator
        });
    } else if (type == 2) {
        rs2 = me.ParseQualityPhotoPlan({
            data: originalData,
            tableConfigId: tableConfigId,
            treeId: treeId,
            init: true,
            creator: creator
        });
    }

    if (rs2.success) {
        res.data = rs2.data;
    } else {
        res.msg = rs2.msg;
    }
} catch (error) {
    res.success = false;
    res.msg = "添加质量数据策划表失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddQualityReportTpl">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddQualityReportTpl
 * @description   添加质量报告模板文件  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {STRING}    nodeName    
 * @param    {STRING}    fileName    
 * @param    {STRING}    filePath    
 * @param    {STRING}    fileFormat    
 * @param    {STRING}    creator    
 * @param    {STRING}    createTime    
 *
 * @returns    {JSON}
 */
var res = {};
var createTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
try {
    fileName = fileName.substring(fileName.lastIndexOf("\\") + 1, fileName.length);
    var selectSql = "select * from quality_report_tpl where nodename='" + nodeName + "'";
    var rs1 = Things["Thing.DB.Oracle"].RunQuery({ sql: selectSql });
    if (rs1.rows.length == 0) {
        //添加
        var insertSql = "insert into QUALITY_REPORT_TPL (ID, NODENAME, FILENAME, FILEPATH, FILEFORMAT, CREATOR, CREATE_TIME)values (QUALITY_REPORT_TPL_SEQ.NEXTVAL,'" + nodeName + "','" + fileName + "','" + filePath + "','" + fileFormat + "','" + creator + "','" + createTime + "')";
        Things["Thing.DB.Oracle"].RunCommand({ sql: insertSql });
    } else {
        //更新
        var updateSql = "update QUALITY_REPORT_TPL set filename='" + fileName + "',filepath='" + filePath + "',fileformat='" + fileFormat + "',creator='" + creator + "',create_time='" + createTime + "' where nodename='" + nodeName + "'";
        Things["Thing.DB.Oracle"].RunCommand({ sql: updateSql });
    }
    res.success = true;
    res.msg = "上传成功";
} catch (error) {
    res.success = false;
    res.msg = "上传失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddQualitySign">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddQualitySign
 * @description   质量数据确认表 增加一条签名信息 wanghq 2021年12月2日18:18:18
 * @implementation    {Script}
 *
 * @param    {INTEGER}    qualityPlanId    
 * @param    {INTEGER}    type    
 * @param    {STRING}    img    
 * @param    {STRING}    creator    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};
try {
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    img = me.StrToClobSql({ str: img });
    var sql = "insert into QUALITY_SIGN (ID, \
        QUALITY_PLAN_ID, \
        IMG, \
        TYPE, \
        CREATOR, \
        CREATE_TIME)values (QUALITY_SIGN_SEQ.NEXTVAL,\
        " + qualityPlanId + ",\
        " + img + ",\
        " + type + ",\
        '" + creator + "',\
        '" + nowTime + "'\
        )";
    Things["Thing.DB.Oracle"].RunCommand({ sql: sql });
} catch (error) {
    res.success = false;
    res.msg = "签名失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddTable
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {STRING}    TREE_NAME    
 * @param    {STRING}    TYPE    
 * @param    {STRING}    MES_INTERFACE    
 * @param    {STRING}    creator    
 * @param    {STRING}    SECOND_DATA_ROWNUM    
 * @param    {STRING}    THREE_DATA_ROWNUM    
 * @param    {INTEGER}    PLAN_START_COLINDEX        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    PLAN_END_COLINDEX        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    PHOTO_START_COLINDEX        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    PHOTO_END_COLINDEX        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    IS_QUERY        {"aspect.defaultValue":"0"}
 *
 * @returns    {NUMBER}
 */
var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
if (TYPE == '3' || TYPE == '6') {
    var sql = "insert into TABLE_CONFIG (ID, TREE_NAME, TYPE, MES_INTERFACE,IS_QUERY,CREATOR, CREATE_TIME)" +
        "values (TABLE_CONFIG_SEQ.nextval,'" + TREE_NAME +
        "', '" + TYPE +
        "', '" + MES_INTERFACE +
        "', '" + IS_QUERY +
        "', '" + creator +
        "', '" + now +
        "')";
    result = Things['Thing.DB.Oracle'].RunCommand({
        sql: sql /* STRING */
    });
} else {
    var tableNameSeq = Things['Thing.DB.Oracle'].RunQuery({
        sql: "select TABLE_NAME_SEQ.nextval NAME_SEQ from dual"
    }).rows[0].NAME_SEQ;

    var tableName = "SECOND_TABLE" + tableNameSeq;
    var seqName = "SECOND_TABLE_SEQ" + tableNameSeq;
    var createTableSql = "create table " + tableName
        + "("
        + "    ID              NUMBER not null"
        + "        primary key,"
        + "    TABLE_CONFIG_ID NUMBER,"
        + "    TREE_ID         NUMBER,"
        + "    PRODUCT_ID      NUMBER,"
        + "    PRODUCT_TREE_IDS      VARCHAR2(4000),"
        + "    STATUS          VARCHAR2(255),"
        + "    FILEPATH        VARCHAR2(255),"
        + "    FILENAME        VARCHAR2(255),"
        + "    CREATOR         VARCHAR2(255),"
        + "    CREATE_TIME     VARCHAR2(255)"
        + ")";

    Things['Thing.DB.Oracle'].RunCommand({
        sql: createTableSql
    });

    var createSeqSql = "create sequence " + seqName + " maxvalue 999999999";
    Things['Thing.DB.Oracle'].RunCommand({
        sql: createSeqSql
    });

    var sql = "insert into TABLE_CONFIG (ID, TREE_NAME, TYPE, MES_INTERFACE,IS_QUERY,SECOND_DATA_ROWNUM,THREE_DATA_ROWNUM,PLAN_START_COLINDEX,PLAN_END_COLINDEX,PHOTO_START_COLINDEX,PHOTO_END_COLINDEX,CREATOR, CREATE_TIME,TABLE_NAME,SEQ_NAME)" +
        "values (TABLE_CONFIG_SEQ.nextval,'" + TREE_NAME +
        "', '" + TYPE +
        "', '" + MES_INTERFACE +
        "', '" + IS_QUERY +
        "', '" + SECOND_DATA_ROWNUM +
        "', '" + THREE_DATA_ROWNUM +
        "', '" + PLAN_START_COLINDEX +
        "', '" + PLAN_END_COLINDEX +
        "', '" + PHOTO_START_COLINDEX +
        "', '" + PHOTO_END_COLINDEX +
        "', '" + creator +
        "', '" + now +
        "', '" + tableName +
        "', '" + seqName +
        "')";
    result = Things['Thing.DB.Oracle'].RunCommand({
        sql: sql /* STRING */
    });
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddTableData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddTableData
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {STRING}    TABLE_CONFIG_ID    
 * @param    {STRING}    TREE_ID    
 * @param    {STRING}    FILEPATH    
 * @param    {STRING}    creator    
 * @param    {STRING}    FILENAME    
 * @param    {JSON}    data    
 * @param    {STRING}    createTime    
 * @param    {STRING}    productId    
 *
 * @returns    {INTEGER}
 */
function isEmpty(colName) {
    var resFlag = true;
    if (colName) {
        for (var key in data) {
            if (colName == key) {
                var value = data[key];
                if (value == null || value == undefined || value == '' || value == " " || value == 'null' || value == "undefined") {

                } else {
                    resFlag = false;
                }
            }

        }
    } else {
        var flag = true;
        for (var key in data) {
            var value = data[key];
            if (value == null || value == undefined || value == '' || value == " " || value == 'null' || value == "undefined") {

            } else {
                flag = false;
            }
        }
        resFlag = flag;
    }
    return resFlag;
}
//去除空行  逻辑：1、首先判断是否有去重更新列 如果有的话 去重更新列的数据是空的情况下 删除 2、不存在去重更新列的情况下 这一行的所有数据都为空的时候删除
//查询参数
var repeatColStr = me.QueryTableSortCol({ tableConfigId: TABLE_CONFIG_ID }).repeat;
//是否需要添加  
var isAdd = false;
if (repeatColStr != "") {
    var isAllEmpty = true;
    var repeatCols = repeatColStr.split(",");
    for (var i = 0; i &lt; repeatCols.length; i++) {
        logger.error(repeatCols[i] + ':' + isEmpty(repeatCols[i]));
        if (!isEmpty(repeatCols[i])) {
            isAllEmpty = false;
            break;
        }
    }
    isAdd = !isAllEmpty;
} else {
    isAdd = !isEmpty();
}
if (isAdd) {

    var table = me.QueryTableById({
        tableId: TABLE_CONFIG_ID /* STRING */
    });
    var tableName = table.rows[0]["TABLE_NAME"];
    var seqName = table.rows[0]["SEQ_NAME"];

    var paramSql = "";
    var valueSql = "";
    for (var key in data) {
        paramSql += "," + key;
        var value = data[key];
        if (value.indexOf("to_clob('") &gt; -1) {
            valueSql += ", " + value + "";
        } else {
            valueSql += ", '" + value + "'";
        }
    }
    var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    if (createTime) {
        now = createTime;
    }
    if (productId == undefined || productId == null) {
        productId = "";
    }
    FILENAME = FILENAME.substring(FILENAME.lastIndexOf("\\") + 1, FILENAME.length);
    var sql = "insert into " + tableName + " (ID, TABLE_CONFIG_ID, TREE_ID,PRODUCT_ID, FILEPATH, FILENAME,CREATOR,CREATE_TIME" + paramSql + ")" +
        "values (" + seqName + ".nextval,'" + TABLE_CONFIG_ID + "', '" + TREE_ID + "', '" + productId + "', '" + FILEPATH + "', '" + FILENAME + "', '" + creator + "', '" + now + "'" + valueSql + ")";
    logger.error(sql);
    result = Things["Thing.DB.Oracle"].RunCommand({
        sql: sql
    });
} else {
    result = 0;
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="AddTestParam">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    AddTestParam
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 *
 * @returns    {NOTHING}
 */
var names = ["序号", "名称", "代号", "要求批次号", "实际批次号", "重量（Kg）", "导热填料要求", "导热填料实施状态", "放气孔应露出数量", "放气孔实露出数量", "保护插头应安装", "保护插头实安装", "镜面要求", "镜面检查", "极性或R脚位置", "极性或R脚位置", "拧紧力矩要求", "拧紧力矩实施值", "紧固件点封要求", "实际紧固件点封", "接地阻值要求值", "接地阻值实测值", "状态"]
var areas = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W'];


for(var i = 0;i&lt;names.length;i++){
	me.AddParam({
		PARAM_NAME: names[i] /* STRING */,
		MES_NAME: "" /* STRING */,
		THREE_AREA: "" /* STRING */,
		creator: "admin" /* STRING */,
		SECOND_AREA: areas[i] /* STRING */,
		TABLE_ID: 85 /* STRING */,
		FORMAT: "0" /* STRING */,
		WIDTH: "40" /* STRING */,
		tableName: "SECOND_TABLE17" /* STRING */,
		RELATION_PARAM: "0" /* INTEGER */,
		VALUE_TYPE: "0" /* INTEGER */
	});
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="BatchSyncAllTables">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**&#xD;
 * @definition    BatchSyncAllTables&#xD;
 * @description   批量同步过程节点的所有二级表数据    wanghq 2025年5月27日10:36:15&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 * @param    {NUMBER}    treeId    过程节点树ID&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
var res = {};&#xD;
try {&#xD;
    var results = [];&#xD;
    &#xD;
    // 查询指定过程节点的所有二级表配置&#xD;
    var tableIdsSql = "select table_id from RELATION_TABLE where NODENAME = (select NODENAME from DATAPACKAGETREE where TREEID = " + treeId + ")";&#xD;
    var tableIdsRs = Things["Thing.DB.Oracle"].RunQuery({&#xD;
        sql: tableIdsSql&#xD;
    });&#xD;
    var tableIds = "";&#xD;
    if (tableIdsRs.rows.length &gt; 0) {&#xD;
        tableIds = tableIdsRs.rows[0].TABLE_ID;&#xD;
    }&#xD;
    var tableConfigSql = "select * from TABLE_CONFIG where ID in (" + tableIds + ") and type!=6 AND MES_INTERFACE IS NOT NULL order by create_time";&#xD;
    var tableConfigs = Things["Thing.DB.Oracle"].RunQuery({ sql: tableConfigSql });&#xD;
    &#xD;
    if (tableConfigs.rows.length == 0) {&#xD;
        res.success = false;&#xD;
        res.msg = "该过程节点下没有配置MES接口的二级表";&#xD;
        res.data = [];&#xD;
    } else {&#xD;
        // 批量同步每个二级表&#xD;
        for (var i = 0; i &lt; tableConfigs.rows.length; i++) {&#xD;
            var tableConfig = tableConfigs.rows[i];&#xD;
            var tableResult = {&#xD;
                tableName: tableConfig.TREE_NAME,&#xD;
                tableId: tableConfig.ID,&#xD;
                mesInterface: tableConfig.MES_INTERFACE,&#xD;
                success: false,&#xD;
                msg: "",&#xD;
                syncCount: 0&#xD;
            };&#xD;
            &#xD;
            try {&#xD;
                // 调用现有的手动同步服务&#xD;
                var syncParams = {&#xD;
                    treeId: treeId,&#xD;
                    type: tableConfig.MES_INTERFACE,&#xD;
                    tableId: tableConfig.ID,&#xD;
                    endY: tableConfig.SECOND_DATA_ROWNUM || "",&#xD;
                    tableType: tableConfig.TYPE&#xD;
                };&#xD;
                &#xD;
                var syncResult = me.manualSyncMes(syncParams);&#xD;
                &#xD;
                if (syncResult.success) {&#xD;
                    tableResult.success = true;&#xD;
                    tableResult.msg = "同步成功";&#xD;
                    tableResult.syncCount = syncResult.result || 0;&#xD;
                } else {&#xD;
                    tableResult.success = false;&#xD;
                    tableResult.msg = syncResult.msg || "同步失败";&#xD;
                }&#xD;
            } catch (syncError) {&#xD;
                tableResult.success = false;&#xD;
                tableResult.msg = "同步异常：" + syncError;&#xD;
            }&#xD;
            &#xD;
            results.push(tableResult);&#xD;
        }&#xD;
        &#xD;
        // 统计同步结果&#xD;
        var successCount = 0;&#xD;
        var failCount = 0;&#xD;
        var totalSyncCount = 0;&#xD;
        &#xD;
        for (var j = 0; j &lt; results.length; j++) {&#xD;
            if (results[j].success) {&#xD;
                successCount++;&#xD;
                totalSyncCount += results[j].syncCount;&#xD;
            } else {&#xD;
                failCount++;&#xD;
            }&#xD;
        }&#xD;
        &#xD;
        res.success = true;&#xD;
        res.msg = "批量同步完成，成功" + successCount + "个表，失败" + failCount + "个表，共同步" + totalSyncCount + "条数据";&#xD;
        res.data = results;&#xD;
        res.summary = {&#xD;
            totalTables: results.length,&#xD;
            successCount: successCount,&#xD;
            failCount: failCount,&#xD;
            totalSyncCount: totalSyncCount&#xD;
        };&#xD;
    }&#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    var msg = "BatchSyncAllTables-批量同步失败，原因：" + error;&#xD;
    res.msg = msg;&#xD;
    res.data = [];&#xD;
}&#xD;
result = res; </code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="CancelDataConfirm">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    CancelDataConfirm
 * @description   取消行内质量数据确认 datetime 2023年7月13日09:26:43
 * @implementation    {Script}
 *
 * @param    {STRING}    onlyValue    
 * @param    {STRING}    paramId    
 * @param    {INTEGER}    tableConfigId    
 * @param    {STRING}    optUser    
 * @param    {NUMBER}    treeId    
 * @param    {INTEGER}    type    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    var selectSql = "select * from QUALITY_SINGLE_CONFIRM where ONLY_VALUES='"
        + onlyValue + "' and table_config_id =" + tableConfigId + " and treeid ="
        + treeId + " and type =" + type + " and PARAM_ID='" + paramId + "'";
    var rs1 = Things["Thing.DB.Oracle"].RunQuery({ sql: selectSql });
    logger.error('selectSql:' + selectSql);
    if (rs1.rows.length == 0) {
        res.success = false;
        res.msg = "未找到相关信息！";
    } else {
        var id = rs1.rows[0]["ID"];
        var deleteSql = "delete from QUALITY_SINGLE_CONFIRM where id=" + id;
        Things["Thing.DB.Oracle"].RunCommand({ sql: deleteSql });
        res.success = true;
        res.msg = "取消成功！";
    }


} catch (error) {
    res.success = false;
    var msg = "取消行内质量数据确认-失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="changeData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    changeData
 * @description   将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 *
 * @returns    {NUMBER}
 */
var result = 0;

result= result + me["changeData_热管"]();
result= result +me["changeData_热控喷涂"]();
result= result +me.changeData_OSR();</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="changeData_OSR">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    changeData_OSR
 * @description   将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 *
 * @returns    {NUMBER}
 */
var sql = "select * from P_OSRPASTE";
var rs = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});

var result = 0;
for(var i = 0;i&lt;rs.rows.length;i++){
	var row = rs.rows[i];
	var d = {};
	d.val1 = "";
	d.val2 = row.VAL5;
	d.val3 = row.VAL6;
	d.val4 = row.VAL7;
	d.val5 = row.VAL8;
	d.val6 = row.VAL9;
	d.val7 = row.VAL10;
	d.val8 = row.VAL11;
	d.val9 = row.VAL12;
	d.val10 = row.VAL13;
	d.val11 = row.VAL14;
	d.val12 = row.VAL15;
	d.val13 = row.VAL16;
	d.val14 = row.VAL17;
	d.val15 = row.VAL18;
	d.val16 = row.VAL19;
	d.val17 = row.VAL20;
	d.val18 = row.VAL21;
	d.val19 = row.VAL22;
	d.val20 = row.VAL23;
	d.colF1 = row.VAL1;
	
	var data = JSON.stringify(d);
	
	// result: NUMBER
	result +=  me.AddTableData({
		creator: row.CREATOR /* STRING */,
		DATA: data /* STRING */,
		TABLE_CONFIG_ID: 63 /* STRING */,
		TREE_ID: row.TREEID /* STRING */,
		FILEPATH: row.FILEPATH /* STRING */,
		FILENAME: row.FILENAME /* STRING */
	});
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="changeData_热控喷涂">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    changeData_热控喷涂
 * @description   将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 *
 * @returns    {NUMBER}
 */
var sql = "select * from P_THERMALCONTROLSPRAYING";
var rs = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});

var result = 0;
for(var i = 0;i&lt;rs.rows.length;i++){
	var row = rs.rows[i];
	var d = {};
	d.val1 = "";
	d.val2 = row.VAL5;
	d.val3 = row.VAL6;
	d.val4 = row.VAL7;
	d.val5 = row.VAL8;
	d.val6 = row.VAL9;
	d.val7 = row.VAL10;
	d.val8 = row.VAL11;
	d.val9 = row.VAL12;
	d.val10 = row.VAL13;
	d.val11 = row.VAL14;
	d.val12 = row.VAL15;
	d.val13 = row.VAL16;
	d.val14 = row.VAL17;
	d.val15 = row.VAL18;
	d.val16 = row.VAL19;
	d.val17 = row.VAL20;
	d.colE1 = row.VAL1;
	
	var data = JSON.stringify(d);
	
	// result: NUMBER
	result +=  me.AddTableData({
		creator: row.CREATOR /* STRING */,
		DATA: data /* STRING */,
		TABLE_CONFIG_ID: 42 /* STRING */,
		TREE_ID: row.TREEID /* STRING */,
		FILEPATH: row.FILEPATH /* STRING */,
		FILENAME: row.FILENAME /* STRING */
	});
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="changeData_热管">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    changeData_热管
 * @description   将旧的数据转换为新的数据  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 *
 * @returns    {NUMBER}
 */
var sql = "select * from P_THERMOTUBEMACHINING";
var rs = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});

var result = 0;
for(var i = 0;i&lt;rs.rows.length;i++){
	var row = rs.rows[i];
	var d = {};
	d.val1 = "";
	d.val2 = row.VAL4;
	d.val3 = row.VAL5;
	d.val4 = row.VAL6;
	d.val5 = row.VAL7;
	d.val6 = row.VAL8;
	d.val7 = row.VAL9;
	d.val8 = row.VAL10;
	d.val9 = row.VAL11;
	d.val10 = row.VAL12;
	d.val11 = row.VAL13;
	d.val12 = row.VAL14;
	d.val13 = row.VAL15;
	d.val14 = row.VAL16;
	d.val15 = row.VAL17;
	d.val16 = row.VAL18;
	d.val17 = row.VAL19;
	d.val18 = row.VAL20;
	d.val19 = row.VAL21;
	d.val20 = row.VAL22;
	d.val21 = row.VAL23;
	d.val22 = row.VAL24;
	d.val23 = row.VAL25;
	d.val24 = row.VAL30;
	d.val25 = row.VAL31;
	d.val26 = row.VAL26;
	d.val27 = row.VAL27;
	d.val28 = row.VAL28;
	d.val29 = row.VAL29;
	d.colE1 = row.VAL1;
	
	var data = JSON.stringify(d);
	
	// result: NUMBER
	result +=  me.AddTableData({
		creator: row.CREATOR /* STRING */,
		DATA: data /* STRING */,
		TABLE_CONFIG_ID: 41 /* STRING */,
		TREE_ID: row.TREEID /* STRING */,
		FILEPATH: row.FILEPATH /* STRING */,
		FILENAME: row.FILENAME /* STRING */
	});
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ClearEmptyRow">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ClearEmptyRow
 * @description   清除现有的二级表中的空行 wanghq 2023年5月4日17:37:40
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var tableRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from table_config where table_name is not null" });
    var delStrArr = [];
    for (var x = 0; x &lt; tableRs.rows.length; x++) {
        var table = tableRs.rows[x];
        var tableName = table.TABLE_NAME;
        var treeName = table.TREE_NAME;
        var tableConfigId = table.ID;
        var delSql = "delete from " + tableName;
        //去除空行  逻辑：1、首先判断是否有去重更新列 如果有的话 去重更新列的数据是空的情况下 删除 2、不存在去重更新列的情况下 这一行的所有数据都为空的时候删除
        //查询参数
        var repeatColStr = me.QueryTableSortCol({ tableConfigId: tableConfigId }).repeat;
        if (repeatColStr != "") {
            var repeatCols = repeatColStr.split(",");
            for (var i = 0; i &lt; repeatCols.length; i++) {
                if (i == 0) {
                    delSql += " where " + repeatCols[i] + " is null";
                } else {
                    delSql += " and " + repeatCols[i] + " is null";
                }
            }
        } else {
            var paramRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from param_config where col_name is not null and table_id=" + tableConfigId });
            for (var i = 0; i &lt; paramRs.rows.length; i++) {
                var cloName = paramRs.rows[i]["COL_NAME"];
                if (i == 0) {
                    delSql += " where " + cloName + " is null";
                } else {
                    delSql += " and " + cloName + " is null";
                }
            }
        }
        logger.error('delSql:' + delSql);
        var delNum = Things['Thing.DB.Oracle'].RunCommand({ sql: delSql });
        //.rows[0].result
        logger.error('delNum:'+delNum);
        if (delNum &gt; 0) {
            delStrArr.push(treeName + "(" + tableConfigId + "," + tableName + ")清除了" + delNum + "条空数据");
        }

    }

    res.success = true;
    res.msg = delStrArr.join("；\n");
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ClearTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ClearTable
 * @description   清除配置表相关数据  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 *
 * @returns    {NOTHING}
 */
//清除配置标关联信息
var clearTableSql = "UPDATE TABLE_CONFIG SET TABLE_NAME='',SEQ_NAME=''";
Things["Thing.DB.Oracle"].RunCommand({
    sql: clearTableSql
});
//清除属性表关联信息
var clearParamSql = "UPDATE PARAM_CONFIG SET TABLE_NAME='',COL_NAME=''";
Things["Thing.DB.Oracle"].RunCommand({
    sql: clearParamSql
});

//删除创建的二级表
var selectDropTableSql = "SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME LIKE 'SECOND_TABLE%'";
var tables = Things["Thing.DB.Oracle"].RunQuery({
    sql: selectDropTableSql /* STRING */
});

for (var x = 0; x &lt; tables.rows.length; x++) {
    var tableName = tables.rows[x]["TABLE_NAME"];
    var dropTableSql = "drop table " + tableName;
    Things["Thing.DB.Oracle"].RunCommand({
        sql: dropTableSql
    });
}
//删除创建的二级表序列
var selectDropSeqSql = "SELECT SEQUENCE_NAME from dba_sequences WHERE SEQUENCE_NAME LIKE 'SECOND_TABLE_SEQ%'";
var seqs = Things["Thing.DB.Oracle"].RunQuery({
    sql: selectDropSeqSql /* STRING */
});

for (var x = 0; x &lt; seqs.rows.length; x++) {
    var seqName = seqs.rows[x]["SEQUENCE_NAME"];
    var dropSeqSql = "drop sequence " + seqName;
    Things["Thing.DB.Oracle"].RunCommand({
        sql: dropSeqSql
    });
}
//初始化表名序列
Things["Thing.DB.Oracle"].RunCommand({
    sql: "drop sequence TABLE_NAME_SEQ"
});
Things["Thing.DB.Oracle"].RunCommand({
    sql: "create sequence TABLE_NAME_SEQ maxvalue 999999999"
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="CompareValue">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    CompareValue
 * @description   wanghq 2022年7月28日15:28:37
 * @implementation    {Script}
 *
 * @param    {STRING}    designStringValue    
 * @param    {STRING}    actualStringValue    
 *
 * @returns    {BOOLEAN}
 */
/**
 * 将字符串转为数字
 * @param {*} value 
 * @returns 
 */
function getNumberValue(value) {
    return Things['Thing.Fn.IndexAnalysis'].GetNumberValue({ value: value });
}

/**
 * 分离设计值 获取起始值和结束值  匹配格式为 25(+0.05,-0.05)
 * @param {*} designStringValue 
 */
function separateDesign1(designStringValue) {
    var tempArr1 = designStringValue.split("("); //['25','+0.05,-0.05)']
    var initNum = Number(tempArr1[0]); // 25
    var tempArr2 = tempArr1[1].split(",");//['+0.05','-0.05)']
    //增量
    var upNum = getNumberValue(tempArr2[0]);
    //减量
    var downNum = getNumberValue(tempArr2[1]);
    var startValue = initNum - downNum;
    var endValue = initNum + upNum;
    return {
        startValue: startValue,
        endValue: endValue,
        isEqulasBetween: true
    }
}


/**
 * 分离设计值 获取起始值和结束值  匹配格式为 优于200mmx200mm:0.1  （实测值的&lt;=“：”右侧数据）
 * @param {*} designStringValue 
 */
function separateDesign2(designStringValue) {
    var tempArr1 = designStringValue.split(":"); //['优于200mmx200mm','0.1']
    var endValue = getNumberValue(tempArr1[1]);
    return {
        startValue: Number.MIN_VALUE,
        endValue: endValue,
        isEqulasBetween: true
    }
}

/**
 * 分离设计值 获取起始值和结束值  匹配格式为 20±5
 * @param {*} designStringValue 
 */
function separateDesign3(designStringValue) {
    var arr = designStringValue.split("±");
    var initValue = getNumberValue(arr[0]);
    var rangeValue = getNumberValue(arr[1]);
    var startValue = initValue - rangeValue;
    var endValue = initValue + rangeValue;
    resValue = {
        startValue: startValue,
        endValue: endValue,
        isEqulasBetween: true
    };
    return resValue;
}


/**
 * 将字符串中的 中文标点符号 替换为英文的  方便做统一处理   替换特殊单位
 * @param {*} stringValue 
 */
function dealChineseCharacters(stringValue) {
    return stringValue.replace(/（/g, "(").replace(/）/g, ")").replace(/，/g, ",").replace(/：/g, ":").replace(/；/g, ";").replace("Pa.m^3/s", "").replace(/ /g, "").trim();
}

/**
 * 获取实测值 可能是一个数字，也可能是区间值比如（90-93） 也可能是多个值(管道1:20；管道2:40)
 * @param {*} actualStringValue 
 */
function getActual(actualStringValue) {
    var resValue = {};
    //判断是否存在；  存在分号任务是多个实测值 存放在数组中
    if (actualStringValue.indexOf(";") &gt; -1) {
        if (actualStringValue.indexOf("满足要求") &gt; -1) {
            //匹配格式 1708;满足要求
            resValue = {
                type: 'number',
                value: getNumberValue(actualStringValue.split(";")[0])
            }
        } else {
            //匹配格式为 管孔1:6.06;管孔2:6.26;管孔3:6.36
            var arr = actualStringValue.split(";");
            var value = [];
            for (var i = 0; i &lt; arr.length; i++) {
                var node = arr[i];
                if (node.indexOf(":") &gt; -1) {
                    value.push(getNumberValue(node.split(":")[1]));
                } else {
                    value.push(getNumberValue(node));
                }
            }
            resValue = {
                type: 'more',
                value: value
            }
        }

    } else if (actualStringValue.indexOf(",") &gt; -1) {
        // 30.12,40.15  存在于多个设计值 对比多个实测值 在一个单元格中
        var arr = actualStringValue.split(",");
        var value = [];
        for (var i = 0; i &lt; arr.length; i++) {
            value.push(getNumberValue(arr[i]));
        }
        resValue = {
            type: 'more',
            value: value
        }
    } else if (/\d+\-\d+/.test(actualStringValue) || /\d+\~\d+/.test(actualStringValue)) {
        actualStringValue = actualStringValue.replace("~", "-");
        var arr = actualStringValue.split("-");
        resValue = {
            value: {
                startValue: getNumberValue(arr[0]),
                endValue: getNumberValue(arr[1])
            },
            type: "between"
        };
    } else {
        resValue = {
            value: getNumberValue(actualStringValue),
            type: "number"
        }
    }
    return resValue;
}

/**
 * 获取设计值的起始值 如果没有开始值或者结束值 开始值用Number.MIN_VALUE 结束值用Number.MAX_VALUE
 * @param {*} designStringValue 
 */
function getDesign(designStringValue) {
    var resValue = {};
    if (!isNaN(Number(designStringValue)) &amp;&amp; /\d+\.?\d*/.test(designStringValue)) {
        // logger.error("type1");
        resValue = {
            startValue: Number(designStringValue),
            endValue: Number(designStringValue),
            isEqulasBetween: true
        };
    } else if (/\d+\.?\d*\(\+?\d+\.?\d*\,\-?\d+\.?\d*\)/.test(designStringValue)) {
        // logger.error("type2");
        // 匹配格式为 25(+0.05,-0.05)
        resValue = separateDesign1(designStringValue);
    } else if (/优于\w+\:\d+\.?\d*/.test(designStringValue)) {
        // logger.error("type3");
        //匹配格式为 优于200mmx200mm:0.1
        resValue = separateDesign2(designStringValue);
    } else if (designStringValue.indexOf("~") &gt; -1) {
        // logger.error("type5");
        //匹配格式为 20~30
        var arr = designStringValue.split("~");
        resValue = {
            startValue: getNumberValue(arr[0]),
            endValue: getNumberValue(arr[1]),
            isEqulasBetween: true
        };
    } else if (designStringValue.indexOf("±") &gt; -1) {
        //存在一个单元格有多个设计值的情况  用，分割
        if (designStringValue.indexOf(",") &gt; -1) {
            // logger.error("type6");
            var arr = designStringValue.split(",");
            resValue = [];
            for (var i = 0; i &lt; arr.length; i++) {
                resValue.push(separateDesign3(arr[i]));
            }
        } else {
            // logger.error("type7");
            //匹配格式为 20±5
            resValue = separateDesign3(designStringValue);
        }
    } else if (designStringValue.indexOf("&gt;=") &gt; -1 || designStringValue.indexOf("≥") &gt; -1 || designStringValue.indexOf("大于等于") &gt; -1 || designStringValue.indexOf("不小于") &gt; -1) {
        // logger.error("type8");
        //匹配格式为 &gt;=20 ≥20 大于等于20 不小于20
        designStringValue = designStringValue.replace("&gt;=", "").replace("≥", "").replace("大于等于", "").replace("不小于", "");
        var startValue = getNumberValue(designStringValue);
        resValue = {
            startValue: startValue,
            endValue: Number.MAX_VALUE,
            isEqulasBetween: true
        };
    } else if (designStringValue.indexOf("&lt;=") &gt; -1 || designStringValue.indexOf("≤") &gt; -1 || designStringValue.indexOf("小于等于") &gt; -1 || designStringValue.indexOf("不大于") &gt; -1) {
        // logger.error("type9");
        //匹配格式为 &lt;=20 ≤20 小于等于20 不大于20
        designStringValue = designStringValue.replace("&lt;=", "").replace("≤", "").replace("小于等于", "").replace("不大于", "");
        var endValue = getNumberValue(designStringValue);
        resValue = {
            startValue: Number.MIN_VALUE,
            endValue: endValue,
            isEqulasBetween: true
        };
    } else if (designStringValue.indexOf("＞") &gt; -1 || designStringValue.indexOf("&gt;") &gt; -1 ||　designStringValue.indexOf("大于") &gt; -1) {
        // logger.error("type10");
        //匹配格式为 &gt;20 大于20
        designStringValue = designStringValue.replace("&gt;", "").replace("＞", "").replace("大于", "");
        var startValue = getNumberValue(designStringValue);
        resValue = {
            startValue: startValue,
            endValue: Number.MAX_VALUE,
            isEqulasBetween: false
        };
    } else if (designStringValue.indexOf("＜") &gt; -1 || designStringValue.indexOf("&lt;") &gt; -1 ||designStringValue.indexOf("小于") &gt; -1) {
        // logger.error("type11");
        //匹配格式为 &lt;20 小于20
        designStringValue = designStringValue.replace("＜", "").replace("&lt;", "").replace("小于", "");
        // logger.error("designStringValue11:" + designStringValue);
        var endValue = getNumberValue(designStringValue);
        resValue = {
            startValue: Number.MIN_VALUE,
            endValue: endValue,
            isEqulasBetween: false
        };
    } else if (designStringValue.indexOf("-") &gt; -1) {
        // logger.error("type4");
        //匹配格式为 20-30
        var arr = designStringValue.split("-");
        resValue = {
            startValue: getNumberValue(arr[0]),
            endValue: getNumberValue(arr[1]),
            isEqulasBetween: true
        };
    } else {
        // logger.error("type12");
        resValue = {
            startValue: Number.MIN_VALUE,
            endValue: Number.MAX_VALUE,
            isEqulasBetween: false
        };
    }
    return resValue;
}


/**
 * 判断实测值是否在区间范围中
 * @param {*} startValue 起始值
 * @param {*} endValue 结束值
 * @param {*} actualValue 实测值
 * @param {*} isEqulasBetween 是否等于两个区间值
 */
function compareBetween(startValue, endValue, actualValue, isEqulasBetween) {
    var flag = true;
    if (isEqulasBetween) {
        if (actualValue &gt;= startValue &amp;&amp; actualValue &lt;= endValue) {
            flag = true;
        } else {
            flag = false;
        }
    } else {
        if (actualValue &gt; startValue &amp;&amp; actualValue &lt; endValue) {
            flag = true;
        } else {
            flag = false;
        }
    }
    return flag;
}

/**
 * 比较实测值是否符合设计值
 * @param {*} designStringValue 
 * @param {*} actualStringValue 
 */
function compareValue(designStringValue, actualStringValue) {
    designStringValue = dealChineseCharacters(designStringValue);
    actualStringValue = dealChineseCharacters(actualStringValue);

    //如果设计值中没有数字 则不需要对比
    if (!/\d+/g.test(designStringValue)) {
        return true;
    }
    //如果设计值和实测值完全相同 不需要对比
    if (designStringValue == actualStringValue) {
        return true;
    }
    var design = getDesign(designStringValue);
    var actual = getActual(actualStringValue);
    if (designStringValue == 'RKTL-DRZ-1导热硅脂') {
        logger.error('design:' + JSON.stringify(design));
        logger.error('actual:' + JSON.stringify(actual));
    }

    var flag = true;
    if (Array.isArray(design)) {
        //如果是数组的话 证明存在多个设计值 规则为 一个设计值对标一个实测值
        //只有实测值为多个的情况下才可以进行比较
        flag = false;
        if (actual.type == 'more') {
            if (actual.value.length &gt;= design.length) {
                flag = true;
                for (var i = 0; i &lt; design.length; i++) {
                    if (!compareBetween(design[i].startValue, design[i].endValue, actual.value[i], design[i].isEqulasBetween)) {
                        flag = false;
                        break;
                    }
                }
            }
        }
    } else {
        //只有一个设计值范围
        if (actual.type == 'number') {
            flag = compareBetween(design.startValue, design.endValue, actual.value, design.isEqulasBetween);
        } else if (actual.type == 'more') {
            var actualValues = actual.value;
            for (var i = 0; i &lt; actualValues.length; i++) {
                var actualValue = actualValues[i];
                if (!compareBetween(design.startValue, design.endValue, actualValue, design.isEqulasBetween)) {
                    flag = false;
                    break;
                }
            }
        } else if (actual.type == 'between') {
            var actualStartValue = actual.value.startValue;
            var actualEndValue = actual.value.endValue;
            flag = false;
            if (design.startValue &lt;= actualStartValue &amp;&amp; design.endValue &gt;= actualEndValue) {
                flag = true;
            }
        }
    }
    return flag;
}
result = compareValue(designStringValue, actualStringValue);</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ConfirmTableData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ConfirmTableData
 * @description   确认质量数据 消除超差数据红色报警 wanghq 2023年12月11日15:14:03
 * @implementation    {Script}
 *
 * @param    {STRING}    tableName    
 * @param    {STRING}    ids    
 * @param    {STRING}    fullname    
 * @param    {NUMBER}    treeId    
 * @param    {NUMBER}    tableConfigId    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //查询参数中的检验人员和检验日期
    var paramSql = "select * from PARAM_CONFIG where TABLE_NAME='" + tableName + "' and FORMAT in ('5','6')";
    var params = Things['Thing.DB.Oracle'].RunQuery({ sql: paramSql }).ToJSON().rows;
    var updateSql = "";
    var idArr = ids.split(",");
    var table = Things['Thing.DB.Oracle'].RunQuery({
        sql: "select * from TABLE_CONFIG where ID='" + tableConfigId + "'"
    }).rows[0];
    var hasCertificate = table.CERTIFICATE_FILEPATH ? true : false;


    for (var j = 0; j &lt; idArr.length; j++) {
        var id = idArr[j];
        if (params.length &gt; 0) {
            var userCol = "", dateCol = "";
            var nowDate = dateFormat(new Date(), "yyyy-MM-dd");
            for (var i = 0; i &lt; params.length; i++) {
                var param = params[i];
                var paramColName = param['COL_NAME'];
                var paramFormat = param['FORMAT'];
                if (paramFormat == '5') {
                    userCol = paramColName;
                } else if (paramFormat == '6') {
                    dateCol = paramColName;
                }
            }
            updateSql = "update " + tableName + " set status='1'";
            if (userCol != "") {
                updateSql += ", " + userCol + "='" + fullname + "'";
            }
            if (dateCol != "") {
                updateSql += ", " + dateCol + "='" + nowDate + "'";
            }
            updateSql += " where tree_id=" + treeId + " and id = " + id;
        } else {
            updateSql = "update " + tableName + " set status='1' where tree_id=" + treeId + " and id = " + id;
        }
        Things['Thing.DB.Oracle'].RunCommand({ sql: updateSql });
        if (hasCertificate) {
            //创建合格证
            var createRes = Things['Thing.Fn.Certificate'].CreateCertificate({
                tableConfigId: tableConfigId,
                dataId: id,
                username: username
            });
        }
    }
    res.success = true;
    res.msg = "确认成功";
} catch (error) {
    res.success = false;
    res.msg = "确认失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ConvertTableHtml">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ConvertTableHtml
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {JSON}    datas    
 *
 * @returns    {STRING}
 */
me.pa</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="CopyTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    CopyTable
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {STRING}    TREE_NAME    
 * @param    {STRING}    TYPE    
 * @param    {STRING}    MES_INTERFACE    
 * @param    {STRING}    creator    
 * @param    {STRING}    SECOND_DATA_ROWNUM    
 * @param    {STRING}    THREE_FILEPATH    
 * @param    {STRING}    SECOND_FILEPATH    
 * @param    {INTEGER}    ID    
 * @param    {STRING}    TABLE_NAME    
 * @param    {STRING}    THREE_DATA_ROWNUM    
 *
 * @returns    {NUMBER}
 */
var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
if (TYPE == '3' || TYPE == '6') {
    var sql = "insert into TABLE_CONFIG (ID, TREE_NAME, TYPE, MES_INTERFACE,CREATOR, CREATE_TIME)" +
        "values (TABLE_CONFIG_SEQ.nextval,'" + TREE_NAME +
        "', '" + TYPE +
        "', '" + MES_INTERFACE +
        "', '" + creator +
        "', '" + now +
        "')";
    result = Things['Thing.DB.Oracle'].RunCommand({
        sql: sql /* STRING */
    });
} else {
    //获取新表序列号
    var tableNameSeq = Things["Thing.DB.Oracle"].RunQuery({
        sql: "select TABLE_NAME_SEQ.nextval NAME_SEQ from dual"
    }).rows[0].NAME_SEQ;

    var tableName = "SECOND_TABLE" + tableNameSeq;
    var seqName = "SECOND_TABLE_SEQ" + tableNameSeq;

    //复制标结果但是不复制表数据
    var copySql = "create table " + tableName + " as select * from " + TABLE_NAME + " where 1=2";

    Things["Thing.DB.Oracle"].RunCommand({
        sql: copySql /* STRING */
    });

    //创建新表的序列
    var createSeqSql = "create sequence " + seqName + " maxvalue 999999999";
    Things['Thing.DB.Oracle'].RunCommand({
        sql: createSeqSql
    });

    //在表格配置表中插入数据
    var insertSql = "insert into TABLE_CONFIG (ID, TREE_NAME, TYPE, MES_INTERFACE,SECOND_DATA_ROWNUM,THREE_DATA_ROWNUM,SECOND_FILEPATH,THREE_FILEPATH, CREATOR, CREATE_TIME,TABLE_NAME,SEQ_NAME)" +
        "values (TABLE_CONFIG_SEQ.nextval,'" + TREE_NAME +
        "', '" + TYPE +
        "', '" + MES_INTERFACE +
        "', '" + SECOND_DATA_ROWNUM +
        "', '" + THREE_DATA_ROWNUM +
        "', '" + SECOND_FILEPATH +
        "', '" + THREE_FILEPATH +
        "', '" + creator +
        "', '" + now +
        "', '" + tableName +
        "', '" + seqName +
        "')";
    Things["Thing.DB.Oracle"].RunCommand({
        sql: insertSql /* STRING */
    });

    //查询新表的id
    var tableId = Things["Thing.DB.Oracle"].RunQuery({
        sql: "select ID from TABLE_CONFIG where TREE_NAME='" + TREE_NAME + "'"
    }).rows[0].ID;


    //在参数配置表中复制一份
    result = Things["Thing.DB.Oracle"].RunCommand({
        sql: "Insert into PARAM_CONFIG(ID, PARAM_NAME, MES_NAME, THREE_AREA, SECOND_AREA,WIDTH,FORMAT,TABLE_ID,CREATOR, CREATE_TIME,TABLE_NAME,COL_NAME) select PARAM_CONFIG_SEQ.nextval,PARAM_NAME, MES_NAME, THREE_AREA, SECOND_AREA,WIDTH,FORMAT,'" + tableId + "',CREATOR, '" + dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss") + "','" + tableName + "',COL_NAME from PARAM_CONFIG where TABLE_ID='" + ID + "'"
    });
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="DealProductId">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    DealProductId
 * @description   所有的二级表 和清单表 新增 product_tree_ids  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //查询所有的二级表名称
    var selSecondTablesSql = "select * from USER_TABLES where TABLE_NAME like 'SECOND_TABLE%'";
    var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: selSecondTablesSql });
    for (var i = 0; i &lt; rs.rows.length; i++) {
        var row = rs.rows[i];
        var tableName = row.TABLE_NAME;
        Things['Thing.DB.Oracle'].RunCommand({ sql: "alter table " + tableName + " add PRODUCT_TREE_IDS VARCHAR (4000)" });
    }
    Things['Thing.DB.Oracle'].RunCommand({ sql: "alter table DESIGN_DATA_RESULT add PRODUCT_TREE_IDS VARCHAR (4000)" });
    Things['Thing.DB.Oracle'].RunCommand({ sql: "alter table CRAFT_DATA_RESULT add PRODUCT_TREE_IDS VARCHAR (4000)" });
    Things['Thing.DB.Oracle'].RunCommand({ sql: "alter table PROCESS_CONTROL_RESULT add PRODUCT_TREE_IDS VARCHAR (4000)" });
    Things['Thing.DB.Oracle'].RunCommand({ sql: "alter table QUALITY_CONTROL_RESULT add PRODUCT_TREE_IDS VARCHAR (4000)" });

    res.success = true;
    res.data = [];
    res.msg = "执行成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="DeleteDownloadFile">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    DeleteDownloadFile
 * @description   删除下载列表中已经下载的文件 datetime 2023年8月7日21:45:52
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    /**
     * 保留五天数据
     */
    var querySql = "select * from QUALITY_DOWNLOAD where (IS_DOWNLOAD=1 and to_timestamp(END_TIME,'YYYY-MM-DD HH24:MI:SS')  &lt; systimestamp - interval '5' DAY)  or (IS_COMPLETE != 1 and to_timestamp(START_TIME, 'YYYY-MM-DD HH24:MI:SS') &lt; systimestamp - interval '1' DAY)";
    var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: querySql }).rows;
    var fileUploadPath = Things["Thing.Fn.SystemDic"].getFileUploadPath();
    
    for (var i = 0; i &lt; rs.length; i++) {
        if (rs[i].IS_COMPLETE == 1) {
            if (rs[i].FILE_PATH) {
                var filePath = fileUploadPath + "//" + rs[i].FILE_PATH;
                logger.error('filePath:' + filePath);
                var url = Things['Thing.UserLogin'].GetNewFileHandleUrl() + "/file/delete?filePath=" + filePath;
                var params = {
                    url: url
                };
                var postText = Resources["ContentLoaderFunctions"].PostText(params);
            }
        }
        Things['Thing.DB.Oracle'].RunCommand({ sql: "delete from QUALITY_DOWNLOAD where id=" + rs[i].ID });
    }
    res.success = true;
    res.data = [];
    res.msg = "删除下载列表中已经下载的文件 成功";
} catch (error) {
    res.success = false;
    var msg = "DeleteDownloadFile-删除下载列表中已经下载的文件 失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="DeleteParam">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    DeleteParam
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {STRING}    ids    
 *
 * @returns    {NUMBER}
 */
var sql = "delete from PARAM_CONFIG  where ID in ("+ids+")";

result = Things['Thing.DB.Oracle'].RunCommand({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="DeletePhoto">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    DeletePhoto
 * @description   质量影像记录查看照片时候删除操作 wanghq 2021年12月9日14:54:10
 * @implementation    {Script}
 *
 * @param    {INTEGER}    id    
 * @param    {STRING}    photoPath    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};

try {

    var sql = "delete from QUALITY_SINGLE_PHOTO where id=" + id;
    Things["Thing.DB.Oracle"].RunCommand({ sql: sql });

    var url = Things['Thing.UserLogin'].GetNewFileHandleUrl() + "/table/delete/photo?filePath=" + photoPath;
    var params = {
        url: url
    };
    Resources["ContentLoaderFunctions"].PostJSON(params);
    res.msg = "删除成功!";
} catch (error) {
    res.success = false;
    res.msg = "删除失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="DeleteTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    DeleteTable
 * @description   wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {STRING}    ID    
 *
 * @returns    {NUMBER}
 */
var sql = "delete from TABLE_CONFIG  where ID='"+ID+"'";

result = Things['Thing.DB.Oracle'].RunCommand({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="DeleteTableData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    DeleteTableData
 * @description   删除质量数据 wanghq 2022年7月18日15:20:52
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableConfigId    
 * @param    {STRING}    tableName    
 * @param    {STRING}    ids    
 * @param    {NUMBER}    treeId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 对应的属性表中的 序号列、排序列和去重列
    var sortJson = me.QueryTableSortCol({ tableConfigId: tableConfigId });

    if (sortJson.repeat != "") {
        var repeat = sortJson.repeat;
        var repeats = repeat.split(",");
        var repeatArr = [];
        var repeatSql = "";
        for (var i = 0; i &lt; repeats.length; i++) {
            repeatArr.push(repeats[i]);
            if (i == repeats.length - 1) {
                repeatSql += " " + repeats[i];
            } else {
                repeatSql += " " + repeats[i] + ",";
            }
        }
        //先查询ID中的去重属性
        var selectSql = "select * from " + tableName + " where id in(" + ids + ")";
        var selDatas = Things['Thing.DB.Oracle'].RunQuery({ sql: selectSql });
        for (var i = 0; i &lt; selDatas.rows.length; i++) {
            var data = selDatas.rows[i];
            var delSql = "delete from " + tableName + " where tree_id=" + treeId + "";
            for (var j = 0; j &lt; repeatArr.length; j++) {
                var repeatCol = repeatArr[j];
                if (data[repeatCol]) {
                    delSql += " and " + repeatCol + "='" + data[repeatCol] + "'";
                } else {
                    delSql += " and " + repeatCol + " is null";
                }

            }
            logger.error('delSql:' + delSql);
            Things['Thing.DB.Oracle'].RunCommand({ sql: delSql });
        }
    } else {
        Things['Thing.DB.Oracle'].RunCommand({ sql: "delete from " + tableName + " where tree_id=" + treeId + " and id in(" + ids + ")" });
    }

    res.success = true;
    res.data = [];
    res.msg = "删除成功";
} catch (error) {
    res.success = false;
    res.msg = "删除失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="GenerateFileComplete">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    GenerateFileComplete
 * @description   生成文件完成 datetime 2023年8月7日17:14:53
 * @implementation    {Script}
 *
 * @param    {STRING}    filePath    
 * @param    {STRING}    fileName    
 * @param    {STRING}    fileSize    
 * @param    {NUMBER}    downloadId    
 * @param    {NUMBER}    isComplete    
 * @param    {STRING}    msg    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    var sql = "update QUALITY_DOWNLOAD set FILE_NAME ='" + fileName + "',FILE_PATH='" + filePath
        + "',FILE_SIZE='" + fileSize + "',IS_COMPLETE=" + isComplete + ",END_TIME='" + nowTime + "',MSG='" + msg + "' where id=" + downloadId;
    logger.error('GenerateFileComplete-sql:' + sql);
    Things['Thing.DB.Oracle'].RunCommand({ sql: sql });
    res.success = true;
    res.data = [];
} catch (error) {
    res.success = false;
    var msg = "GenerateFileComplete-生成文件完成失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="GenerateQualityReportByTreeId">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    GenerateQualityReportByTreeId
 * @description   生成质量报告  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    treeId    
 *
 * @returns    {JSON}
 */
var res = {};

try {
    //查询tree名称
    var nodeName = Things["Thing.DB.Oracle"].RunQuery({ sql: "select NODENAME from datapackagetree where treeid=" + treeId }).rows[0]["NODENAME"];

    var sql = "select * from quality_report_tpl where nodename='" + nodeName + "'";
    var rs = Things["Thing.DB.Oracle"].RunQuery({ sql: sql }).ToJSON().rows;
    if (rs.length == 0) {
        res.success = false;
        res.msg = "暂未上传质量报告模板！";
    } else {
        res.data = rs[0];
        res.success = true;
        res.msg = "";
    }
} catch (error) {
    res.success = false;
    res.msg = "生成质量报告出错，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="getChildNodeInTree">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    getChildNodeInTree
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    pid    
 *
 * @returns    {INFOTABLE}
 */
var result = Things["Thing.DB.Oracle"].RunQuery({
 sql: "select * from DATAPACKAGETREE where PARENTID='" + pid + "'"
});

if (result != undefined &amp;&amp; result.rows.length &gt; 0) {
 var tableLength = result.rows.length;
 for (var x = 0; x &lt; tableLength; x++) {
  var row = result.rows[x];
  var childResult = me.getChildNodeInTree({
   pid: row.TREEID /* STRING */
  });
  
  // result: INFOTABLE
  result = Resources["InfoTableFunctions"].Union({
   t1: result /* INFOTABLE */ ,
   t2: childResult /* INFOTABLE */
  });

 }
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="GetNewParamSeq">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    GetNewParamSeq
 * @description   通过表名获取列的最新序列号  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    tableName    
 *
 * @returns    {INTEGER}
 */
var sql = "select column_name,data_type ,data_length,data_precision,data_scale,table_name from user_tab_columns where TABLE_NAME = '" + tableName + "' and COLUMN_NAME like 'V%'";
var res = Things["Thing.DB.Oracle"].RunQuery({
    sql: sql /* STRING */
});
result = res.rows.length + 1;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="GetProductTypeByTreeId">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    GetProductTypeByTreeId
 * @description   根据树节点ID获取质量数据类型，二三级表类型+excel导入类型+自动采集的质量数据类型  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var querySql = "select * from TABLE_CONFIG where type!='6'";
var result = [];
if (treeId != 1) {
    var sql1 = "select table_id from relation_table where nodename in (SELECT NODENAME FROM DATAPACKAGETREE where TREEID in (select TREEID from DATAPACKAGETREE s start with s.TREEID='" + treeId + "' CONNECT by  PRIOR s.TREEID = s.PARENTID))";
    var rs1 = Things["Thing.DB.Oracle"].RunQuery({
        sql: sql1 /* STRING */
    });

    if (rs1.rows.length &gt; 0) {
        var ids = [];
        for (var i = 0; i &lt; rs1.getRowCount(); i++) {
            if (rs1.rows[i].TABLE_ID) {
                ids.push(rs1.rows[i].TABLE_ID);
            }
        }
        if (ids.length &gt; 0) {
            var tableIds = ids.join(",");
            querySql = "select * from TABLE_CONFIG where ID in (" + tableIds + ") and type!=6 order by create_time";
        } else {
            querySql = "select * from TABLE_CONFIG where 1!=1";
        }
    } else {
        querySql = "select * from TABLE_CONFIG where 1!=1";
    }
}
var rs2 = Things["Thing.DB.Oracle"].RunQuery({
    sql: querySql /* STRING */
});
for (var i = 0; i &lt; rs2.rows.length; i++) {
    var row = rs2.rows[i];
    var obj = {};
    obj.id = row.ID;
    obj.name = row.TREE_NAME || "";
    obj.ctype = row.TYPE || "";
    obj.spath = row.SECOND_FILEPATH || "";
    obj.tpath = row.THREE_FILEPATH || "";
    obj.endy = row.SECOND_DATA_ROWNUM || "";
    obj.mtype = row.MES_INTERFACE || "";
    obj.planPath = row.PLAN_FILEPATH || "";
    obj.planStartColIndex = row.PLAN_START_COLINDEX || "";
    obj.planEndColIndex = row.PLAN_END_COLINDEX || "";
    obj.hasCertificate = row.CERTIFICATE_FILEPATH ? true : false;
    obj.type = "1"; //代表类型为 二、三级表的类型
    result.push(obj);

    if (row.TREE_NAME == '电缆网元器件汇总表') {
        var obj1 = {};
        obj1.id = row.ID;
        obj1.name = '电缆网元器件汇总原始表';
        obj1.ctype = row.TYPE || "";
        obj1.spath = row.SECOND_FILEPATH || "";
        obj1.tpath = row.THREE_FILEPATH || "";
        obj1.endy = row.SECOND_DATA_ROWNUM || "";
        obj1.mtype = row.MES_INTERFACE || "";
        obj1.planPath = row.PLAN_FILEPATH || "";
        obj1.planStartColIndex = row.PLAN_START_COLINDEX || "";
        obj1.planEndColIndex = row.PLAN_END_COLINDEX || "";
        obj1.type = "1"; //代表类型为 二、三级表的类型
        result.push(obj1);
    }
}
var sql3 = "select * from DICTIONARY_DATA where pid in( SELECT id FROM DICTIONARY where name in('Excel导入类型','质量统计清单'))";
var rs3 = Things["Thing.DB.Oracle"].RunQuery({
    sql: sql3 /* STRING */
});
for (var x = 0; x &lt; rs3.rows.length; x++) {
    var d = rs3.rows[x];
    var key = d.KEY;
    var rs4 = Things["Thing.Fn.DataSearch"].QueryQualityDataCount({
        tableType: key /* STRING */,
        treeid: treeId,
        username: username
    });
    if (rs4 &gt; 0) {
        var obj1 = {};
        obj1.name = d.NAME;
        obj1.type = "2"; //代表类型为 Excel导入类型和质量统计清单
        obj1.ctype = key;
        result.push(obj1);
    }
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="GetQualityPhotoTypeByTreeId">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    GetQualityPhotoTypeByTreeId
 * @description   wanghq 2021年11月25日10:30:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 *
 * @returns    {JSON}
 */
var querySql = "select * from TABLE_CONFIG";
 var result = [];
 if (treeId != 1) {
     var sql1 = "select table_id from relation_table where nodename in (SELECT NODENAME FROM DATAPACKAGETREE where TREEID in (select TREEID from DATAPACKAGETREE s start with s.TREEID='" + treeId + "' CONNECT by  PRIOR s.TREEID = s.PARENTID))";
 
     var rs1 = Things["Thing.DB.Oracle"].RunQuery({
         sql: sql1 /* STRING */
     });
 
     if (rs1.rows.length &gt; 0) {
         var ids = [];
         for (var i = 0; i &lt; rs1.getRowCount(); i++) {
             if (rs1.rows[i].TABLE_ID) {
                 ids.push(rs1.rows[i].TABLE_ID);
             }
         }
         if (ids.length &gt; 0) {
             var tableIds = ids.join(",");
             querySql = "select * from TABLE_CONFIG where ID in (" + tableIds + ") and photo_filepath is not null order by create_time";
         } else {
             querySql = "select * from TABLE_CONFIG where 1!=1";
         }
     } else {
         querySql = "select * from TABLE_CONFIG where 1!=1";
     }
 }
 var rs2 = Things["Thing.DB.Oracle"].RunQuery({
     sql: querySql /* STRING */
 });
 for (var i = 0; i &lt; rs2.rows.length; i++) {
     var row = rs2.rows[i];
     var obj = {};
     obj.id = row.ID;
     obj.name = row.TREE_NAME || "";
     obj.ctype = row.TYPE || "";
     obj.spath = row.SECOND_FILEPATH || "";
     obj.tpath = row.THREE_FILEPATH || "";
     obj.endy = row.SECOND_DATA_ROWNUM || "";
     obj.mtype = row.MES_INTERFACE || "";
     obj.planPath = row.PLAN_FILEPATH || "";
     obj.planStartColIndex = row.PLAN_START_COLINDEX || "";
     obj.planEndColIndex = row.PLAN_END_COLINDEX || "";
     obj.photoPath = row.PHOTO_FILEPATH || "";
     obj.photoStartColIndex = row.PHOTO_START_COLINDEX || "";
     obj.photoEndColIndex = row.PHOTO_END_COLINDEX || "";
     obj.type = "1"; //代表类型为 二、三级表的类型
     result.push(obj);
 }</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="GetSecondTableHeader">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    GetSecondTableHeader
 * @description   获取二级表的表头  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    id    
 *
 * @returns    {JSON}
 */
var table = me.QueryTableById({
    tableId: id /* STRING */
});
var tableLength = table.getRowCount();
if (tableLength &gt; 0) {
    var type = table.rows[0].TYPE;
    var second_filepath = table.rows[0].SECOND_FILEPATH;
    if (type == "4") {
        second_filepath = table.rows[0].THREE_FILEPATH;
    }

    var endY = table.rows[0].SECOND_DATA_ROWNUM;
    second_filepath = encodeURIComponent(second_filepath);
    var url = Things['Thing.UserLogin'].GetNewFileHandleUrl() + "/table/second/header?filePath=" + second_filepath + "&amp;endY=" + endY + "&amp;tableId=" + id;
    var params = {
        url: url /* STRING */
    };
    var result = Resources["ContentLoaderFunctions"].GetJSON(params);
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="GetTableDataSql">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    GetTableDataSql
 * @description   获取查询二级表数据的sql  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    productTreeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    processTreeId        {"aspect.defaultValue":"-1"}
 * @param    {INTEGER}    table_config_id    
 * @param    {JSON}    query        {"aspect.PP-264deacd-0679-49c8-856b-19f8662535d0":"{\"defaultValue1665197764296\":\"PP-264deacd-0679-49c8-856b-19f8662535d0-defaultValue1665197764296\"}","aspect.defaultValue":"{\"params\":[]}"}
 *
 * @returns    {STRING}
 */
//表格配置表对象
var tableConfig = me.QueryTableById({ tableId: table_config_id });

//数据库结构化表名称
var tableName = tableConfig.rows[0]["TABLE_NAME"];

// 对应的属性表中的 序号列、排序列和去重列
var sortJson = me.QueryTableSortCol({ tableConfigId: table_config_id });

//序号列 可能存在多个
var indexColNames = sortJson.indexColName;
var indexColNameArr = indexColNames.split(",");

//查询数据库中的表字段 去除序号列的查询
var selectColSql = "select column_name from user_tab_cols where table_name='" + tableName + "'";
if (indexColNames != "") {
    selectColSql += " and column_name not in (";
    for (var i = 0; i &lt; indexColNameArr.length; i++) {
        if (i == indexColNameArr.length - 1) {
            selectColSql += "'" + indexColNameArr[i] + "'";
        } else {
            selectColSql += "'" + indexColNameArr[i] + "',";
        }
    }
    selectColSql += ")";
}

//查询出所有显示的字段
var cols = Things["Thing.DB.Oracle"].RunQuery({
    sql: selectColSql
});
var colsArr = [];

//添加型号的查询
colsArr.push("(select NODENAME from DATAPACKAGETREE where NODETYPE='product' start with TREEID=s2.TREE_ID connect by prior PARENTID=TREEID) as MODEL");
//添加阶段的查询
colsArr.push("(select NODENAME from DATAPACKAGETREE where NODETYPE='phase' start with TREEID=s2.TREE_ID connect by prior PARENTID=TREEID) as PHASE");
for (var x = 0; x &lt; cols.rows.length; x++) {
    var colName = "s2." + cols.rows[x]["COLUMN_NAME"];
    colsArr.push(colName);
}

colsArr.push("'" + tableName + "' AS TABLE_NAME");
//字段查询Sql
var colSql = colsArr.join(",");

//查询数据的总sql
var selectSql = "";

function checkNotEmptyId(myId) {
    if (myId !== -1 &amp;&amp; myId !== 0 &amp;&amp; myId !== 1 &amp;&amp; myId !== '1' &amp;&amp; myId !== 1.0 &amp;&amp; myId !== '1.0' &amp;&amp; myId !== '0' &amp;&amp; myId !== '0.0' &amp;&amp; myId != '-1' &amp;&amp; myId != '-1.0') {
        return true;
    }
    return false;
}

var whereSql = " where 1=1";

var queryUser = query.queryUser || ""; //查询用户
var queryParams = query.params || [];
var sqlUserDataTags = Things['Thing.Fn.AitScreen'].GetUserDataTag({ username: queryUser }).data.sqlUserDataTag;
if (sqlUserDataTags.length == 0 &amp;&amp; queryUser !== 'all') {
    //无权限 
    whereSql += " and 1!=1";
} else {
    var getTreeIdSql = "select TREEID from TREE_ALL_VIEW where NODETYPE in ('dir','leaf') ";
    if (sqlUserDataTags.length &gt; 0) {
        getTreeIdSql += "and model_code in (" + sqlUserDataTags.join(',') + ")"
    }
    if (checkNotEmptyId(processTreeId)) {
        //查询过程结构树的节点
        getTreeIdSql = getTreeIdSql + " start with TREEID IN (" + processTreeId + ") CONNECT by  PRIOR TREEID = PARENTID";
    }
    whereSql += " and s2.tree_id in (" + getTreeIdSql + ")";

    if (checkNotEmptyId(productTreeId)) {
        //查询产品结构树关联的数据ID
        var getRelationIdSql = "select RELATION_ID from PRODUCT_RELATION  where TREE_ID in (select ID from PRODUCT_TREE start with ID = " + productTreeId + " connect by prior ID = PID) and RELATION_NAME = '" + tableName + "'";
        whereSql += " and s2.id in (" + getRelationIdSql + ")";
    }

    if (queryParams.length &gt; 0) {
        for (var i = 0; i &lt; queryParams.length; i++) {
            var param = queryParams[i];
            if (param.value != "") {
                whereSql += " and S2." + param.name + " like '%" + param.value + "%'";
            }
        }
    }

}

//排序Sql
var sortSql = "";
if (sortJson.sort.length &gt; 0) {
    var sortColName = sortJson.sort[0].colName;
    var sortParamName = sortJson.sort[0].paramName;
    if (sortParamName == '工序'||sortParamName == '序号') {
        //将字母和数据分离 先按照字母排序 再按照数字排序
        sortSql = " order by REGEXP_SUBSTR(s2." + sortColName + ",'^\\D*') nulls first,TO_NUMBER(REGEXP_SUBSTR(s2." + sortColName + ",'\\d+')),s2.CREATE_TIME desc";
    } else {
        //纯粹按照拼音排序
        sortSql = " order by NLSSORT(s2." + sortColName + ",'NLS_SORT=SCHINESE_PINYIN_M'),s2.CREATE_TIME desc";
    }
} else {
    sortSql = " order by s2.CREATE_TIME desc";
}

if (sortJson.repeat != "") {
    var repeat = sortJson.repeat;
    var repeats = repeat.split(",");
    var repeatSql = "";
    for (var i = 0; i &lt; repeats.length; i++) {
        if (i == repeats.length - 1) {
            repeatSql += " s2." + repeats[i];
        } else {
            repeatSql += " s2." + repeats[i] + ",";
        }
    }
    selectSql = "select s.* from ( select row_number() over (partition by " + repeatSql + " order by s2.CREATE_TIME desc) s1, " + colSql + " from " + tableName + " s2 " + whereSql + sortSql + ") s where s1=1";
} else {
    selectSql = "select " + colSql + " from " + tableName + " s2 " + whereSql + sortSql;
}

var rownumSql = ",ROWNUM,ROWNUM as rowno";
if (indexColNames != "") {
    for (var i = 0; i &lt; indexColNameArr.length; i++) {
        rownumSql += ",ROWNUM as " + indexColNameArr[i];
    }
}

var result = "select t.*" + rownumSql + " from (" + selectSql + ") t";</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="isCapital">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    isCapital
 * @description   判断字符串是否只有大写字母  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    str    
 *
 * @returns    {BOOLEAN}
 */
var reg = /^[A-Z]+$/;
result = reg.test(str);</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="manualSyncMes">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    manualSyncMes
 * @description   wanghq 2021年12月28日10:11:02
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    type    
 * @param    {STRING}    tableId    
 * @param    {STRING}    endY    
 * @param    {INTEGER}    tableType    
 *
 * @returns    {JSON}
 */
var res = {};
var reqUrl = Things["Thing.Fn.SystemDic"].getKeyByNames({
    name: "MES项目清单接口路径",
    pname: "系统配置"
});

var reqUrl1 = Things["Thing.Fn.SystemDic"].getKeyByNames({
    name: "试验管控系统接口路径1",
    pname: "系统配置"
});

try {
    if (type.indexOf("S:") &gt; -1) {
        res = me.reqTestControl({
            treeId: treeId /* STRING */,
            type: type.substr(2) /* STRING */,
            reqUrl: reqUrl1 /* STRING */,
            tableId: tableId /* STRING */,
            endY: endY /* STRING */,
            tableType: tableType
        });
    } else {
        res = me.testMes({
            treeId: treeId /* STRING */,
            type: type /* STRING */,
            reqUrl: reqUrl /* STRING */,
            tableId: tableId /* STRING */,
            endY: endY /* STRING */,
            tableType: tableType
        });
    }
} catch (e) {
    res.success = false;
    res.msg = e;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="NumToChinese">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    NumToChinese
 * @description   阿拉伯数字转中文数字  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    num    
 *
 * @returns    {STRING}
 */
//阿拉伯数字转中文数字
if (!/^\d*(\.\d*)?$/.test(num)) {
    logger.error("Number is wrong!");
}
var AA = new Array("零", "一", "二", "三", "四", "五", "六", "七", "八", "九");
var BB = new Array("", "十", "百", "千", "万", "亿", "点", "");
var a = ("" + num).replace(/(^0*)/g, "").split("."),
    k = 0,
    re = "";
for (var i = a[0].length - 1; i &gt;= 0; i--) {
    switch (k) {
        case 0:
            re = BB[7] + re;
            break;
        case 4:
            if (!new RegExp("0{4}\\d{" + (a[0].length - i - 1) + "}$").test(a[0])) re = BB[4] + re;
            break;
        case 8:
            re = BB[5] + re;
            BB[7] = BB[5];
            k = 0;
            break;
    }
    if (k % 4 == 2 &amp;&amp; a[0].charAt(i + 2) != 0 &amp;&amp; a[0].charAt(i + 1) == 0) re = AA[0] + re;
    if (a[0].charAt(i) != 0) re = AA[a[0].charAt(i)] + BB[k % 4] + re;
    k++;
}
if (a.length &gt; 1) {
    //加上小数部分(如果有小数部分)
    re += BB[6];
    for (var i = 0; i &lt; a[1].length; i++) re += AA[a[1].charAt(i)];
}
result = re;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ParseQualityPhotoPlan">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ParseQualityPhotoPlan
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    data    
 * @param    {INTEGER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 * @param    {INTEGER}    type        {"aspect.defaultValue":"2"}
 * @param    {BOOLEAN}    init        {"aspect.defaultValue":"false"}
 * @param    {STRING}    creator    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};
try {
    data = JSON.parse(data);
    if (data.length &lt;= 1) {
        res.success = false;
        res.msg = "上传的影像记录策划表没有内容！";
    } else {
        var table = me.QueryTableById({ tableId: tableConfigId }).rows[0];
        var tableName = table["TABLE_NAME"];
        var treeName = table["TREE_NAME"];
        //MES接口标识
        var mesInterface = table["MES_INTERFACE"];
        var photoStartColIndex = table["PHOTO_START_COLINDEX"];
        var photoEndColIndex = table["PHOTO_END_COLINDEX"];

        var params = me.QueryParamsById({ tableId: tableConfigId }).ToJSON().rows;
        //策划文件的第一行 为表头
        var names = data[0];

        var indexStr = "序号";

        //获取所有的去重的属性（一条数据的唯一标识）
        var onlyParams = [];
        //共有的名称数组
        var onlyNames = [indexStr];
        //共有的名称数组在names中的索引
        var onlyNameIndexs = [];
        //共有的属性名称对应的字段名称
        var onlyColNames = [];

        for (var i = 0; i &lt; params.length; i++) {
            var param = params[i];
            if (param.IS_REMOVE_REPEAT == "1") {
                onlyParams.push(param);
                onlyNames.push(param.PARAM_NAME);
                onlyNameIndexs.push(names.indexOf(param.PARAM_NAME));
                onlyColNames.push(param.COL_NAME);
            }
        }

        var photoReqStr = "拍照要素";
        var actualPhotoStr = "实际照片";
        var photoNumStr = "照片数量";
        var statusStr = "状态";
        var confirmerStr = "确认人";

        //单表的表头
        var cols = onlyNames.concat([photoReqStr, actualPhotoStr, photoNumStr, statusStr, confirmerStr]);

        var statusIndex = names.indexOf(statusStr);

        //根据属性名称查询属性
        function queryParamByName(paramName) {
            var p = undefined;
            for (var i = 0; i &lt; params.length; i++) {
                if (params[i]["PARAM_NAME"] == paramName) {
                    p = params[i];
                    break;
                }
            }
            return p;
        }

        var arrays = [];
        for (var i = 1, j = photoStartColIndex - 1; j &lt; photoEndColIndex; j++) {
            var relationParam = queryParamByName(names[j]);
            if (relationParam == undefined) {
                continue;
            }
            var obj = {
                name: "（" + me.NumToChinese({ num: i }) + "）" + treeName + names[j],
                cols: cols,
                relationParam: relationParam,
                nameIndex: j,
                datas: []
            };
            arrays.push(obj);
            i++;
        }

        //查询该过程节点下的所有手动上传的质量影像记录
        var allPhotoDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: "select PHOTO_NUMBER,PHOTO_FORMAT,PHOTO_PATH,ID,ONLY_VALUES,PARAM_ID from QUALITY_SINGLE_PHOTO where tree_id=" + treeId + " and TABLE_CONFIG_ID=" + tableConfigId + " order by PHOTO_NUMBER" }).ToJSON().rows;


        var majorNodeSql = "select TREEID" +
            " from DATAPACKAGETREE" +
            " start with TREEID = (" +
            "select TREEID from DATAPACKAGETREE where NODETYPE = 'dir' start with TREEID = " + treeId + " connect by prior PARENTID = TREEID)" +
            " connect by prior TREEID = PARENTID";
        if (type == 1) {
            majorNodeSql = treeId;
        }
        //查询该过程上一层节点下的所有从mes系统采集过来的质量影像记录
        //20240118 修改为 质量影像记录中的自动采集照片增加一个过程节点的判断条件
        var allAutoPhotoSql = "select OBJECT_CODE,OBJECT_TYPE,OBJECT_BATCH,OBJECT_NAME,FILEPATH from (select * from (select ID, FILE_NAME, TABLENAME, FILEPATH from RESULTGATHER a where FILE_TYPE = '影像记录' " +
            "and GATHERING_METHOD = '自动采集' and NODECODE in (select ID from DATA_PACKAGE where REFTREEID in (" + majorNodeSql + "))) " +
            "a left join (select RESULT_ID, PHOTONAME, DOWNLOADURL, SOURCE_ID, OBJECT_TYPE, OBJECT_BATCH, OBJECT_CODE, OBJECT_NAME from "
            + "XMLDATA_PHOTO) b on a.ID = b.RESULT_ID and b.SOURCE_ID like '%' || a.TABLENAME || '%') s where s.OBJECT_TYPE = '" + mesInterface + "'";
        var allAutoPhotos = Things['Thing.DB.Oracle'].RunQuery({ sql: allAutoPhotoSql }).ToJSON().rows;

        function dealPrefix(sort, num) {
            if (!num) {
                num = 2;
            }
            var sortStr = sort + "";
            if (sortStr.length &lt; num) {
                var temp = "";
                for (var i = 0; i &lt; (num - sortStr.length); i++) {
                    temp += "0";
                }
                sortStr = temp + sort;
            }
            return sortStr;
        }

        /**
         * 
         * @param {*} onlyObj 唯一标识对象
         * 
         * @returns {*} 替换唯一标识key值为MES接口的key
         */
        function dealOnlyObj2MesObj(onlyObj) {
            var newObj = {};
            for (var key in onlyObj) {
                for (var x = 0; x &lt; onlyParams.length; x++) {
                    var colName = onlyParams[x]['COL_NAME'];
                    if (key == colName) {
                        var interfaceName = onlyParams[x]['INTERFACE_NAME'];
                        if (interfaceName) {
                            newObj[interfaceName] = onlyObj[key];
                        }
                        break;
                    }
                }
            }
            return newObj;
        }

        /**
         *  在手动的照片基础上添加自动同步的照片
         * @param {*} photo 手动上传的照片
         * @param {*} onlyObj 唯一标识对象
         */
        function getAutoPhoto(photo, onlyObj) {
            var newObj = dealOnlyObj2MesObj(onlyObj);
            var keyNum = Object.keys(newObj).length;
            var autoPhotos = [];
            var yesKeyNum = 0, noKeyNum = 0, noKeys = [];
            for (var x = 0; x &lt; allAutoPhotos.length; x++) {
                var autoPhoto = allAutoPhotos[x];
                var oneYesKeyNum = 0;
                var oneNoKeys = [];
                for (var key in newObj) {
                    if (autoPhoto[key] !== newObj[key]) {
                        oneNoKeys.push(newObj[key]);
                    } else {
                        oneYesKeyNum++;
                    }
                }
                if (oneNoKeys.length == 0) {
                    autoPhotos.push(autoPhoto);
                } else if (oneNoKeys.length == 1) {
                    if (noKeys.indexOf(oneNoKeys[0]) == -1) {
                        noKeys.push(oneNoKeys[0]);
                    }
                }
                yesKeyNum = yesKeyNum &gt;= oneYesKeyNum ? yesKeyNum : oneYesKeyNum;
                noKeyNum = keyNum - yesKeyNum;

            }

            var nameArr = [];
            for (var key in onlyObj) {
                nameArr.push(onlyObj[key]);
            }
            var names = nameArr.join("_");
            for (var x = 0; x &lt; autoPhotos.length; x++) {
                var autoPhoto = autoPhotos[x];
                autoPhoto['TYPE'] = 'auto';
                autoPhoto['PHOTO_PATH'] = autoPhoto['FILEPATH'];
                var num = names + '-' + dealPrefix(x + 1, 3) + '(自动)';
                autoPhoto['PHOTO_NUMBER'] = num;
            }
            if (yesKeyNum &lt; keyNum &amp;&amp; noKeyNum == 1) {
                autoPhotos.push({
                    "TYPE": 'noKeys',
                    "noKeys": noKeys
                });
            }
            if (autoPhotos.length &gt; 0) {
                photo = photo.concat(autoPhotos);
            }

            return photo;
        }

        //根据 名称、代号、要求批次号和属性ID获取照片
        function getPhoto(onlyObj, paramId) {
            var photo = [];
            for (var i = 0; i &lt; allPhotoDatas.length; i++) {
                var t = allPhotoDatas[i];
                var onlyValues = JSON.parse(t["ONLY_VALUES"]);
                var tFlag = true;
                for (var key in onlyObj) {
                    if (onlyObj[key] !== onlyValues[key]) {
                        tFlag = false;
                        break;
                    }
                }
                if (tFlag &amp;&amp; t["PARAM_ID"] == paramId) {
                    t["TYPE"] = "upload";
                    photo.push(t);
                }
            }
            if (onlyParams.length == 0) {
                return photo;
            }
            return getAutoPhoto(photo, onlyObj);
        }

        //查询该过程节点下的所有质量数据确认信息
        var allConfirmDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: "select a.*,b.USER_FULLNAME from QUALITY_SINGLE_CONFIRM a left join SYS_USER b on a.CONFIRMER=b.USER_NAME  where a.TREEID=" + treeId + " and type=2 and a.TABLE_CONFIG_ID=" + tableConfigId }).rows;
        //根据名称、代号、批次号 和 属性id获取确认人

        function getConfirmUser(onlyObj, paramId) {
            var user = "";
            for (var i = 0; i &lt; allConfirmDatas.length; i++) {
                var t = allConfirmDatas[i];
                var onlyValues = JSON.parse(t["ONLY_VALUES"]);
                var tFlag = true;
                for (var key in onlyObj) {
                    if (onlyObj[key] !== onlyValues[key]) {
                        tFlag = false;
                        break;
                    }
                }
                if (tFlag &amp;&amp; t["PARAM_ID"] == paramId) {
                    user = t["USER_FULLNAME"];
                    break;
                }
            }
            return user;
        }

        //查询该过程节点下的所有质量数据状态信息
        var allStatusDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: "select a.*,b.USER_FULLNAME from QUALITY_SINGLE_STATUS a left join SYS_USER b on a.CREATOR=b.USER_NAME  where a.TREEID=" + treeId + " and a.TYPE=2 and a.TABLE_CONFIG_ID=" + tableConfigId }).rows;
        //根据名称、代号、批次号 和 属性id获取确认人
        function getStatus(onlyObj, paramId) {
            var status = "";
            for (var i = 0; i &lt; allStatusDatas.length; i++) {
                var t = allStatusDatas[i];
                var onlyValues = JSON.parse(t["ONLY_VALUES"]);
                var tFlag = true;
                for (var key in onlyObj) {
                    if (onlyObj[key] !== onlyValues[key]) {
                        tFlag = false;
                        break;
                    }
                }
                if (tFlag &amp;&amp; t["PARAM_ID"] == paramId) {
                    status = t["STATUS"];
                    break;
                }
            }
            return status;
        }

        for (var i = 0; i &lt; arrays.length; i++) {
            var obj = arrays[i];
            var relationParam = obj.relationParam;
            var relationParamColName = relationParam["COL_NAME"];
            var relationParamName = relationParam["PARAM_NAME"];
            var relationParamId = relationParam["ID"];
            var selfNameIndex = obj.nameIndex; //要求属性在表头数组中的索引
            //遍历excel数据 除去第一行
            var datas = [];
            var onlyValuesArr = [];
            var onlyNamesValueArr = [];
            for (var j = 1, x = 1; j &lt; data.length; j++) {
                var d = data[j];
                var onlyValues = [];
                var onlyObj = {};
                var onlyNamesValue = {};
                for (var o = 0; o &lt; onlyNameIndexs.length; o++) {
                    onlyObj[onlyColNames[o]] = d[onlyNameIndexs[o]];
                    onlyNamesValue[onlyNames[o + 1]] = d[onlyNameIndexs[o]];
                    onlyValues.push(d[onlyNameIndexs[o]]);
                }
                var statusValue = statusIndex == -1 ? "" : d[statusIndex];

                //策划表中的要求值
                var requiredValue = d[selfNameIndex];

                if (requiredValue != "" &amp;&amp; requiredValue != "/" &amp;&amp; requiredValue != "\\") {
                    var photo = getPhoto(onlyObj, relationParamId);
                    var newPhoto = [];
                    //20241030 筛选出自动采集中不符合要求的 名称 编号 批次号中的一个属性
                    for (var p = 0; p &lt; photo.length; p++) {
                        var node = photo[p];
                        if (node['TYPE'] == 'noKeys') {
                            var noKeys = node['noKeys'];
                            for (var o = 0; o &lt; onlyValues.length; o++) {
                                var v = onlyValues[o];
                                if (noKeys.indexOf(v) &gt; -1) {
                                    onlyValues[o] = v + ":noKey";
                                    for (var paramName in onlyNamesValue) {
                                        if (onlyNamesValue[paramName] == v) {
                                            onlyNamesValue[paramName] = v + ":noKey";
                                        }
                                    }
                                }
                            }
                        } else {
                            newPhoto.push(node);
                        }
                    }
                    //确认人
                    var confirmer = getConfirmUser(onlyObj, relationParamId);
                    //更改之后的状态
                    var updateStatus = getStatus(onlyObj, relationParamId);
                    if (updateStatus == "" &amp;&amp; statusValue !== "" &amp;&amp; init) {
                        //在QUALITY_SINGLE_STATUS表中添加数据
                        me.UpdateQualityDataStatus({
                            onlyValue: JSON.stringify(onlyObj),
                            paramId: String(relationParamId),
                            tableConfigId: tableConfigId,
                            paramName: relationParamName,
                            creator: creator,
                            treeId: treeId,
                            type: 2,
                            paramValue: '',
                            status: statusValue
                        });
                    }

                    updateStatus = updateStatus == "" ? statusValue : updateStatus;
                    onlyValues.unshift(x);

                    datas.push(onlyValues.concat([requiredValue, JSON.stringify(newPhoto), newPhoto.length, updateStatus, confirmer]));
                    onlyValuesArr.push(onlyObj);
                    onlyNamesValueArr.push(onlyNamesValue);
                    x++;
                }
            }
            obj.datas = datas;
            obj.onlyValues = onlyValuesArr;
            obj.onlyNamesValueArr = onlyNamesValueArr;
            delete obj.nameIndex;
        }
        res.success = true;
        res.msg = "查询成功";
        res.data = arrays;
    }
} catch (error) {
    logger.error("加载质量影像记录出错，原因：" + error);
    res.success = false;
    res.msg = "加载质量影像记录出错，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ParseQualityPlan">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ParseQualityPlan
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    data    
 * @param    {INTEGER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 * @param    {BOOLEAN}    init        {"aspect.defaultValue":"false"}
 * @param    {STRING}    creator    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};
try {
    data = JSON.parse(data);
    if (data.length &lt;= 1) {
        res.success = false;
        res.msg = "上传的策划表没有内容,请重新上传！";
    } else {
        var table = me.QueryTableById({ tableId: tableConfigId }).rows[0];
        var tableName = table["TABLE_NAME"];
        var treeName = table["TREE_NAME"];
        var planStartColIndex = table["PLAN_START_COLINDEX"];
        var planEndColIndex = table["PLAN_END_COLINDEX"];
        var params = me.QueryParamsById({ tableId: tableConfigId }).ToJSON().rows;

        //策划文件的第一行 为表头
        var names = data[0];

        var indexStr = "序号";

        //获取所有的去重的属性（一条数据的唯一标识）
        var onlyParams = [];
        //共有的名称数组
        var onlyNames = [indexStr];
        //共有的名称数组在names中的索引
        var onlyNameIndexs = [];
        //共有的属性名称对应的字段名称
        var onlyColNames = [];

        //基本信息的属性
        var baseParams = [];
        var baseNames = [];
        var baseColNames = [];
        var baseParamsIds = [];
        for (var i = 0; i &lt; params.length; i++) {
            var param = params[i];
            if (param.IS_REMOVE_REPEAT == "1") {
                onlyParams.push(param);
                onlyNames.push(param.PARAM_NAME);
                onlyNameIndexs.push(names.indexOf(param.PARAM_NAME));
                onlyColNames.push(param.COL_NAME);
            }
            if (param.IS_BASE == "1") {
                baseParams.push(param);
                baseNames.push(param.PARAM_NAME);
                baseColNames.push(param.COL_NAME);
                baseParamsIds.push(param.ID);
            }
        }

        var statusStr = "状态";
        var confirmerStr = "确认人";
        var statusIndex = names.indexOf(statusStr);

        //根据属性名称查询属性
        function queryParamByName(paramName) {
            var p = undefined;
            for (var i = 0; i &lt; params.length; i++) {
                if (params[i]["PARAM_NAME"] == paramName) {
                    p = params[i];
                    break;
                }
            }
            return p;
        }

        //根据属性名称查询相关联的属性
        function queryRelationParamByName(paramName) {
            var paramId;
            for (var i = 0; i &lt; params.length; i++) {
                if (params[i]["PARAM_NAME"] == paramName) {
                    paramId = params[i]["ID"];
                    break;
                }
            }
            var relationParam = undefined;
            for (var i = 0; i &lt; params.length; i++) {
                if (params[i]["RELATION_PARAM"] == paramId) {
                    relationParam = params[i];
                    break;
                }
            }
            return relationParam;
        }

        //存储 属性信息的数组
        var arrays = [];
        var tableIndex = 1;
        if (baseParams.length &gt; 0) {
            var obj = {
                name: "（" + me.NumToChinese({ num: tableIndex }) + "）" + treeName + "基本信息",
                relationParamName: baseNames.join(","),
                relationParamId: baseParamsIds.join(","),
                isBase: 1,
                cols: onlyNames.concat(baseNames.concat([statusStr, confirmerStr])),
                datas: []
            };
            arrays.push(obj);
            tableIndex++;
        }

        //是因为需要把重量等基本信息作为一张子表
        for (var i = tableIndex, j = planStartColIndex - 1; j &lt; planEndColIndex; j++) {
            var obj = {};
            var nameStr = names[j];
            if (nameStr.indexOf("-") &gt; -1) {
                nameStr = nameStr.split("-")[0];
            }
            var relationParam = queryRelationParamByName(names[j]);
            if (relationParam == undefined) {
                continue;
            }
            obj = {
                name: "（" + me.NumToChinese({ num: i }) + "）" + treeName + nameStr,
                relationParam: relationParam,
                isBase: 0,
                nameIndex: j,
                cols: onlyNames.concat([names[j], relationParam["PARAM_NAME"], statusStr, confirmerStr]),
                datas: []
            };
            arrays.push(obj);
            i++;
        }

        //查询该过程节点下的所有质量数据
        var allTableDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: "select * from " + tableName + " where tree_id=" + treeId + " order by create_time desc" }).rows;

        //去除字符串前后空白字符
        function trim(str) {
            str = str || "";
            return str.replace(/(^\s*)|(\s*$)/g, "");
        }

        /**
         * @description 根据 共有属性的值和属性名称获取实测值
         *
         * @param {Object} onlyObj 共有属性的值
         * @param {String} paramColName 属性名称
         *
         * @returns {String} 该属性的实测值
         */
        function getActualValue(onlyObj, paramColName) {
            var value = "";
            for (var i = 0; i &lt; allTableDatas.length; i++) {
                var t = allTableDatas[i];
                var tFlag = true;
                for (var key in onlyObj) {
                    if (trim(t[key]) !== trim(onlyObj[key])) {
                        tFlag = false;
                        break;
                    }
                }
                if (tFlag) {
                    value = t[paramColName] || "";
                    break;
                }
            }
            return value;
        }
        //查询该过程节点下的所有质量数据确认信息
        var allConfirmDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: "select a.*,b.USER_FULLNAME from QUALITY_SINGLE_CONFIRM a left join SYS_USER b on a.CONFIRMER=b.USER_NAME  where a.TREEID=" + treeId + " and a.TYPE=1 and a.TABLE_CONFIG_ID=" + tableConfigId }).rows;

        /**
         * @description 根据 共有属性的值和属性id获取确认信息
         *
         * @param {Array} onlyObj 共有属性的值
         * @param {String} paramId 属性id
         *
         * @returns {String} 确认信息
         */
        function getConfirmInfo(onlyObj, paramId) {
            var info = {
                user: "",
                value: ""
            };
            for (var i = 0; i &lt; allConfirmDatas.length; i++) {
                var t = allConfirmDatas[i];
                var onlyValues = JSON.parse(t["ONLY_VALUES"]);
                var tFlag = true;
                for (var key in onlyObj) {
                    if (trim(onlyObj[key]) !== trim(onlyValues[key])) {
                        tFlag = false;
                        break;
                    }
                }
                if (tFlag &amp;&amp; t["PARAM_ID"] == paramId) {
                    info.user = t["USER_FULLNAME"] || "";
                    info.value = t["PARAM_VALUE"] || "";
                    break;
                }
            }
            return info;
        }

        //查询该过程节点下的所有质量数据状态信息
        var allStatusDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: "select a.*,b.USER_FULLNAME from QUALITY_SINGLE_STATUS a left join SYS_USER b on a.CREATOR=b.USER_NAME  where a.TREEID=" + treeId + " and a.TYPE=1 and a.TABLE_CONFIG_ID=" + tableConfigId }).rows;

        /**
         * @description 根据 共有属性的值和属性id获取数据的状态值
         *
         * @param {Array} onlyObj 共有属性的值
         * @param {String} paramId 属性id
         *
         * @returns {String} 状态信息
         */
        function getStatus(onlyObj, paramId) {
            var status = "";
            for (var i = 0; i &lt; allStatusDatas.length; i++) {
                var t = allStatusDatas[i];
                var onlyValues = JSON.parse(t["ONLY_VALUES"]);
                var tFlag = true;
                for (var key in onlyObj) {
                    if (trim(onlyObj[key]) !== trim(onlyValues[key])) {
                        tFlag = false;
                        break;
                    }
                }
                if (tFlag &amp;&amp; t["PARAM_ID"] == paramId) {
                    status = t["STATUS"] || "";
                    break;
                }
            }
            return status;
        }

        for (var i = 0; i &lt; arrays.length; i++) {
            var obj = arrays[i];
            var isBase = obj.isBase;
            if (isBase == 1) {
                var datas = [];
                var onlyValuesArr = [];
                for (var j = 1, x = 1; j &lt; data.length; j++) {
                    var onlyObj = {};
                    var d = data[j];
                    var onlyValues = [];
                    for (var o = 0; o &lt; onlyNameIndexs.length; o++) {
                        onlyObj[onlyColNames[o]] = d[onlyNameIndexs[o]];
                        onlyValues.push(d[onlyNameIndexs[o]]);
                    }
                    var statusValue = statusIndex == -1 ? "" : d[statusIndex];

                    var actualValues = [];
                    for (var o = 0; o &lt; baseColNames.length; o++) {
                        actualValues.push(getActualValue(onlyObj, baseColNames[o]));
                    }
                    //确认信息
                    var confirmeInfo = getConfirmInfo(onlyObj, baseParamsIds.join(","));
                    var confirmer = confirmeInfo.user;
                    var confirmValue = confirmeInfo.value;

                    if (confirmer != "") {
                        logger.error("confirmValue:" + JSON.stringify(confirmValue));
                        actualValues = confirmValue.split(",");
                    }
                    //更改之后的状态
                    var updateStatus = getStatus(onlyObj, baseParamsIds.join(","));
                    if (updateStatus == "" &amp;&amp; statusValue !== "" &amp;&amp; init) {
                        //在QUALITY_SINGLE_STATUS表中添加数据
                        me.UpdateQualityDataStatus({
                            onlyValue: JSON.stringify(onlyObj),
                            paramId: baseParamsIds.join(","),
                            tableConfigId: tableConfigId,
                            paramName: baseNames.join(","),
                            creator: creator,
                            treeId: treeId,
                            type: 1,
                            paramValue: actualValues.join(","),
                            status: statusValue
                        });
                    }


                    updateStatus = updateStatus == "" ? statusValue : updateStatus;
                    onlyValues.unshift(x);
                    var tep = onlyValues.concat(actualValues);
                    datas.push(tep.concat([updateStatus, confirmer]));
                    onlyValuesArr.push(onlyObj);
                    x++;
                }
                obj.datas = datas;
                obj.onlyValues = onlyValuesArr;
            } else {
                var relationParam = obj.relationParam;
                var relationParamColName = relationParam["COL_NAME"];
                var relationParamName = relationParam["PARAM_NAME"];
                var relationParamId = relationParam["ID"];
                var selfNameIndex = obj.nameIndex; //要求属性在表头数组中的索引
                //遍历excel数据 除去第一行
                var datas = [];
                var onlyValuesArr = [];
                for (var j = 1, x = 1; j &lt; data.length; j++) {
                    var d = data[j];
                    var onlyValues = [];
                    var onlyObj = {};
                    for (var o = 0; o &lt; onlyNameIndexs.length; o++) {
                        onlyObj[onlyColNames[o]] = d[onlyNameIndexs[o]];
                        onlyValues.push(d[onlyNameIndexs[o]]);
                    }
                    var statusValue = statusIndex == -1 ? "" : d[statusIndex];
                    //策划表中的要求值
                    var requiredValue = d[selfNameIndex];

                    if (requiredValue != "" &amp;&amp; requiredValue != "/" &amp;&amp; requiredValue != "\\") {
                        var actualValue = getActualValue(onlyObj, relationParamColName);
                        //确认信息
                        var confirmeInfo = getConfirmInfo(onlyObj, relationParamId);
                        var confirmer = confirmeInfo.user;
                        var confirmValue = confirmeInfo.value;
                        if (confirmer != "") {
                            actualValue = confirmValue;
                        }
                        //更改之后的状态
                        var updateStatus = getStatus(onlyObj, relationParamId);

                        if (updateStatus == "" &amp;&amp; statusValue !== "" &amp;&amp; init) {
                            //在QUALITY_SINGLE_STATUS表中添加数据
                            me.UpdateQualityDataStatus({
                                onlyValue: JSON.stringify(onlyObj),
                                paramId: String(relationParamId),
                                tableConfigId: tableConfigId,
                                paramName: relationParamName,
                                creator: creator,
                                treeId: treeId,
                                type: 1,
                                paramValue: actualValue,
                                status: statusValue
                            });
                        }
                        updateStatus = updateStatus == "" ? statusValue : updateStatus;
                        onlyValues.unshift(x);
                        datas.push(onlyValues.concat([requiredValue, actualValue, updateStatus, confirmer]));
                        onlyValuesArr.push(onlyObj);
                        x++;
                    }
                }
                obj.datas = datas;
                obj.onlyValues = onlyValuesArr;
                obj.relationParamName = relationParam["PARAM_NAME"];
                obj.relationParamId = relationParam["ID"];
                delete obj.nameIndex;
            }
        }
        res.success = true;
        res.msg = "查询成功";
        res.data = arrays;
    }
} catch (error) {
    res.success = false;
    res.msg = "加载质量数据出错，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QualityDataConfirm">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QualityDataConfirm
 * @description   行内质量数据确认 wanghq 2021年11月23日15:32:49
 * @implementation    {Script}
 *
 * @param    {STRING}    onlyValue    
 * @param    {STRING}    paramId    
 * @param    {INTEGER}    tableConfigId    
 * @param    {STRING}    paramName    
 * @param    {STRING}    confirmer    
 * @param    {NUMBER}    treeId    
 * @param    {INTEGER}    type    
 * @param    {STRING}    paramValue        {"aspect.defaultValue":" "}
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};

try {
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    var selectSql = "select * from QUALITY_SINGLE_CONFIRM where ONLY_VALUES='" + onlyValue + "' and table_config_id =" + tableConfigId + " and treeid =" + treeId + " and type =" + type + " and PARAM_ID='" + paramId + "'";
    var rs1 = Things["Thing.DB.Oracle"].RunQuery({ sql: selectSql });
    if (rs1.rows.length == 0) {
        //添加操作
        var insertSql = "insert into QUALITY_SINGLE_CONFIRM (ID, CONFIRMER, ONLY_VALUES, TABLE_CONFIG_ID, PARAM_ID,  TREEID,PARAM_NAME,PARAM_VALUE,  TYPE,CREATE_TIME)values (QUALITY_CONFIRM_SEQ.NEXTVAL,'" + confirmer + "','" + onlyValue + "'," + tableConfigId + ",'" + paramId + "'," + treeId + ",'" + paramName + "','" + paramValue + "'," + type + ",'" + nowTime + "')";
        Things["Thing.DB.Oracle"].RunCommand({ sql: insertSql });
    } else {
        //更新操作
        var id = rs1.rows[0]["ID"];
        var updateSql = "update QUALITY_SINGLE_CONFIRM set CONFIRMER='" + confirmer + "',CREATE_TIME='" + nowTime + "' where id=" + id;
        Things["Thing.DB.Oracle"].RunCommand({ sql: updateSql });
    }
    res.msg = "确认成功";
} catch (error) {
    res.success = false;
    res.msg = "确认失败，原因：" + error;
}

result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryAllPhoto">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryAllPhoto
 * @description   查询影像记录策划表中的所有照片 wanghq 2023年2月16日11:03:21
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var myPhotos = [];
    var rs = me.QueryProcessQualityPhotoData({
        tableConfigId: tableConfigId,
        treeId: treeId
    });
    if (rs.success) {
        var datas = rs.data;
        for (var i = 0; i &lt; datas.length; i++) {
            var table = datas[i];
            var cols = table.cols;
            var rows = table.datas;
            var photoIndex = cols.indexOf('实际照片');
            for (var j = 0; j &lt; rows.length; j++) {
                var row = rows[j];
                var photoStr = row[photoIndex];
                var photos = JSON.parse(photoStr);
                for (var x = 0; x &lt; photos.length; x++) {
                    var photo = photos[x];
                    photo.PHOTO_NUMBER = photo.PHOTO_NUMBER + '(内部)';
                }
                myPhotos = myPhotos.concat(photos);
            }
        }
        res.success = true;
        res.data = myPhotos;
        res.msg = "成功";
    } else {
        res = rs;
    }

} catch (error) {
    res.success = false;
    res.msg = "查询照片信息失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryAllTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryAllTable
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {INFOTABLE}
 */
var sql = "select * from TABLE_CONFIG order by create_time";
var result = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryDownloadTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryDownloadTable
 * @description   查询下载列表 datetime 2023年8月7日21:05:09
 * @implementation    {Script}
 *
 * @param    {STRING}    creator    
 * @param    {NUMBER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 * @param    {NUMBER}    type_    
 * @param    {INTEGER}    page    
 * @param    {INTEGER}    limit    
 *
 * @returns    {JSON}
 */
var res = {
    code: 0,
    msg: "",
    count: 0,
    data: []
};
try {
    var startRowno = (page - 1) * limit + 1;
    var endRowno = page * limit;
    var querySql = "select *  " +
        "from QUALITY_DOWNLOAD  " +
        "where CREATOR = '" + creator + "'  " +
        "  and TABLE_CONFIG_ID = " + tableConfigId + "  " +
        "  and TREE_ID = " + treeId + "  " +
        "  and TYPE_ = " + type_ + " order by start_time desc";
    var selectPageSql = "select * from (select s.* ,ROWNUM rowno from (" + querySql + ") s) where  rowno &gt;= " + startRowno + " and  rowno &lt;=" + endRowno;
    var pageData = Things["Thing.DB.Oracle"].RunQuery({ sql: selectPageSql });
    var selectCountSql = "select count(*) count from (" + querySql + ") t ";
    res.count = Things["Thing.DB.Oracle"].RunQuery({ sql: selectCountSql }).rows[0]["COUNT"];
    res.data = pageData.ToJSON().rows;
} catch (error) {
    res.success = false;
    res.code = 500;
    var msg = "QueryDownloadTable-查询下载列表失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryMesTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryMesTable
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableId        {"aspect.defaultValue":"0"}
 *
 * @returns    {INFOTABLE}
 */
var sql = "select * from TABLE_CONFIG where MES_INTERFACE is not null and THREE_FILEPATH is not null";
if(tableId!=0){
    sql+= " and id="+tableId;
}

var result = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryParams">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryParams
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    tree_name    
 *
 * @returns    {INFOTABLE}
 */
var sql = "select * from PARAM_CONFIG where TABLE_ID=(select ID from TABLE_CONFIG where TREE_NAME='"+tree_name+"') order by SECOND_AREA";
logger.error("QueryParams_sql:"+sql);
var result = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryParamsById">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryParamsById
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    tableId    
 *
 * @returns    {INFOTABLE}
 */
var sql = "select * from PARAM_CONFIG where TABLE_ID='"+tableId+"'";

var result = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryProcessQualityData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryProcessQualityData
 * @description   查询过程节点的质量数据确认表  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //查询质量数据策划表
    var sql = "select * from quality_plan where type=1 and TABLE_CONFIG_ID=" + tableConfigId + " and treeid=" + treeId;
    var rs = Things["Thing.DB.Oracle"].RunQuery({ sql: sql });
    if (rs.rows.length &gt; 0) {
        var data = rs.rows[0]["DATA"];
        var filepath = rs.rows[0]["FILEPATH"];
        var filename = rs.rows[0]["FILENAME"];
        var id = rs.rows[0]["ID"];
        res = me.ParseQualityPlan({
            data: data,
            tableConfigId: tableConfigId,
            treeId: treeId
        });
        var signRes = me.QueryQualitySign({ qualityPlanId: id });
        if (signRes.success) {
            res.signs = signRes.data;
        }
        res.filepath = filepath;
        res.filename = filename;
        res.id = id;
    } else {
        res.success = false;
        res.msg = "暂未上传策划表！";
    }
} catch (error) {
    res.success = false;
    res.msg = "查询过质量数据确认表失败，原因:" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryProcessQualityPhotoData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryProcessQualityPhotoData
 * @description   查询过程节点的质量数据确认表  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //查询质量数据策划表
    var sql = "select * from quality_plan where type=2 and TABLE_CONFIG_ID=" + tableConfigId + " and treeid=" + treeId;
    var rs = Things["Thing.DB.Oracle"].RunQuery({ sql: sql });
    if (rs.rows.length &gt; 0) {
        var data = rs.rows[0]["DATA"];
        var filepath = rs.rows[0]["FILEPATH"];
        var filename = rs.rows[0]["FILENAME"];
        var id = rs.rows[0]["ID"];
        res = me.ParseQualityPhotoPlan({
            data: data,
            tableConfigId: tableConfigId,
            treeId: treeId
        });
        var signRes = me.QueryQualitySign({ qualityPlanId: id });
        if (signRes.success) {
            res.signs = signRes.data;
        }
        res.filepath = filepath;
        res.filename = filename;
        res.id = id;
    } else {
        res.success = false;
        res.msg = "暂未上传策划表！";
    }
} catch (error) {
    res.success = false;
    res.msg = "查询质量影像记录出错，原因：" + error;
}

result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryQualityPhotoSummary">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryQualityPhotoSummary
 * @description   查询质量影像记录汇总表 wanghq 2021年12月28日14:24:05
 * @implementation    {Script}
 *
 * @param    {NUMBER}    tree_id    
 * @param    {INTEGER}    table_config_id    
 *
 * @returns    {JSON}
 */
var res = {};

var planResult = Things["Thing.DB.Oracle"].RunQuery({
    sql: "select * from quality_plan where type=2 and table_config_id=" + table_config_id + " and treeid=" + tree_id
});
if (planResult.rows.length == 0) {
    res.success = false;
    res.data = [];
    res.msg = "暂未上传策划表！";
} else {
    var params = me.QueryParamsById({ tableId: table_config_id }).ToJSON().rows;

    //获取所有的去重的属性(一条数据的唯一标识)
    var onlyParams = [];
    //共有的名称数组
    var onlyNames = [];
    //共有的属性名称对应的字段名称
    var onlyColNames = [];

    for (var i = 0; i &lt; params.length; i++) {
        var param = params[i];
        if (param.IS_REMOVE_REPEAT == "1") {
            onlyParams.push(param);
            onlyNames.push(param.PARAM_NAME);
            onlyColNames.push(param.COL_NAME);
        }
    }

    var table = me.QueryTableById({ tableId: table_config_id }).ToJSON().rows[0];

    var tableName = table["TABLE_NAME"];
    var treeName = table["TREE_NAME"];
    //MES接口标识
    var mesInterface = table["MES_INTERFACE"];

    var photoStartColIndex = table["PHOTO_START_COLINDEX"];
    var photoEndColIndex = table["PHOTO_END_COLINDEX"];

    var planData = JSON.parse(planResult.rows[0]["DATA"]);
    var names = planData[0];

    var statusStr = "状态";
    var photoNumStr = "照片数量";
    var photoStr = "照片";

    var statusIndex = names.indexOf(statusStr);

    //根据属性名称查询属性字段名
    function queryParamColNameByName(paramName) {
        var colName = undefined;
        for (var i = 0; i &lt; params.length; i++) {
            if (params[i]["PARAM_NAME"] == paramName) {
                colName = params[i]["COL_NAME"];
                break;
            }
        }
        return colName;
    }

    //根据属性名称查询属性ID
    function queryParamIdByName(paramName) {
        var id = undefined;
        for (var i = 0; i &lt; params.length; i++) {
            if (params[i]["PARAM_NAME"] == paramName) {
                id = params[i]["ID"];
                break;
            }
        }
        return id;
    }

    //该节点下的该类型的所有质量影像记录
    var allPhotoDatas = Things["Thing.DB.Oracle"]
        .RunQuery({
            sql: "select * from QUALITY_SINGLE_PHOTO where tree_id=" + tree_id + " and table_config_id=" + table_config_id
        })
        .ToJSON().rows;

    var majorNodeSql = "select TREEID" +
        " from DATAPACKAGETREE" +
        " start with TREEID = (" +
        "select TREEID from DATAPACKAGETREE where NODETYPE = 'dir' start with TREEID = " + tree_id + " connect by prior PARENTID = TREEID)" +
        " connect by prior TREEID = PARENTID";
    //查询该过程上一层节点下的所有从mes系统采集过来的质量影像记录
    var allAutoPhotoSql = "select * from (select * from (select ID, FILE_NAME, TABLENAME, FILEPATH from RESULTGATHER a where FILE_TYPE = '影像记录' " +
        "and GATHERING_METHOD = '自动采集' and NODECODE in (select ID from DATA_PACKAGE where REFTREEID in (" + majorNodeSql + "))) " +
        "a left join (select RESULT_ID, PHOTONAME, DOWNLOADURL, SOURCE_ID, OBJECT_TYPE, OBJECT_BATCH, OBJECT_CODE, OBJECT_NAME from "
        + "XMLDATA_PHOTO) b on a.ID = b.RESULT_ID and b.SOURCE_ID like '%' || a.TABLENAME || '%') s where s.OBJECT_TYPE = '" + mesInterface + "'";
    var allAutoPhotos = Things['Thing.DB.Oracle'].RunQuery({ sql: allAutoPhotoSql }).ToJSON().rows;

    function dealPrefix(sort, num) {
        if (!num) {
            num = 2;
        }
        var sortStr = sort + "";
        if (sortStr.length &lt; num) {
            var temp = "";
            for (var i = 0; i &lt; (num - sortStr.length); i++) {
                temp += "0";
            }
            sortStr = temp + sort;
        }
        return sortStr;
    }

    /**
     * 
     * @param {*} onlyObj 唯一标识对象
     * 
     * @returns {*} 替换唯一标识key值为MES接口的key
     */
    function dealOnlyObj2MesObj(onlyObj) {
        var newObj = {};
        for (var key in onlyObj) {
            for (var x = 0; x &lt; onlyParams.length; x++) {
                var colName = onlyParams[x]['COL_NAME'];
                if (key == colName) {
                    var interfaceName = onlyParams[x]['INTERFACE_NAME'];
                    if (interfaceName) {
                        newObj[interfaceName] = onlyObj[key];
                    }
                    break;
                }
            }
        }
        return newObj;
    }
    /**
     *  在手动的照片基础上添加自动同步的照片
     * @param {*} photo 手动上传的照片
     * @param {*} onlyObj 唯一标识对象
     */
    function getAutoPhoto(photo, onlyObj) {
        var newObj = dealOnlyObj2MesObj(onlyObj);
        var autoPhotos = [];
        for (var x = 0; x &lt; allAutoPhotos.length; x++) {
            var autoPhoto = allAutoPhotos[x];
            var flag = true;
            for (var key in newObj) {
                if (autoPhoto[key] !== newObj[key]) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                autoPhotos.push(autoPhoto);
            }
        }

        var nameArr = [];
        for (var key in onlyObj) {
            nameArr.push(onlyObj[key]);
        }
        var names = nameArr.join("_");

        for (var x = 0; x &lt; autoPhotos.length; x++) {
            var autoPhoto = autoPhotos[x];
            autoPhoto['TYPE'] = 'auto';
            autoPhoto['PHOTO_PATH'] = autoPhoto['FILEPATH'];
            var num = names + '-' + dealPrefix(x + 1, 3) + '(自动)';
            autoPhoto['PHOTO_NUMBER'] = num;
        }
        if (autoPhotos.length &gt; 0) {
            photo = photo.concat(autoPhotos);
        }

        return photo;
    }
    //根据 名称、代号、要求批次号和属性ID获取照片
    function getPhoto(onlyObj, paramId) {
        var photo = [];
        for (var i = 0; i &lt; allPhotoDatas.length; i++) {
            var t = allPhotoDatas[i];
            var onlyValues = JSON.parse(t["ONLY_VALUES"]);
            var tFlag = true;
            for (var key in onlyObj) {
                if (onlyObj[key] !== onlyValues[key]) {
                    tFlag = false;
                    break;
                }
            }
            if (tFlag &amp;&amp; t["PARAM_ID"] == paramId) {
                t["TYPE"] = "upload";
                photo.push(t);
            }
        }
        if (onlyParams.length == 0) {
            return photo;
        }
        return getAutoPhoto(photo, onlyObj);
    }

    var newNames = [];
    for (var j = 0; j &lt; names.length; j++) {
        //添加照片数量列
        if (j == names.length - 1) {
            newNames.push(photoNumStr);
        }
        newNames.push(names[j]);
        if (j &gt;= photoStartColIndex - 1 &amp;&amp; j &lt; photoEndColIndex) {
            newNames.push(names[j] + photoStr);
        }
    }

    var datas = [];
    for (var j = 1; j &lt; planData.length; j++) {
        var newData = [];
        newData.push(j + "");
        var d = planData[j];

        var onlyObj = {};
        //共有属性
        for (var x = 1; x &lt; photoStartColIndex - 1; x++) {
            var onlyParamName = newNames[x];
            if (onlyNames.indexOf(onlyParamName) &gt; -1) {
                var onlyParamColName = queryParamColNameByName(onlyParamName);
                onlyObj[onlyParamColName] = d[x];
            }
            newData.push(d[x]);
        }

        var statusValue = d[statusIndex];

        var photoNumTotal = 0;
        for (var x = photoStartColIndex - 1; x &lt; photoEndColIndex; x++) {
            newData.push(d[x]);
            var paramName = names[x];
            var paramId = queryParamIdByName(paramName);
            if (paramId == undefined) {
                newData.push("");
            } else {
                var photos = getPhoto(onlyObj, paramId);
                var photoArr = [];
                var photoStr = "";

                var uploadNum = 0;
                var photo360Num = 0;
                var autoNum = 0;
                for (var p = 0; p &lt; photos.length; p++) {
                    photoNumTotal++;
                    var photo = photos[p];
                    if (photo.TYPE == 'auto') {
                        autoNum++;
                    } else {
                        if (photo.PHOTO_FORMAT == 'zip' || photo.PHOTO_FORMAT == 'rar') {
                            photo360Num++;
                        } else {
                            uploadNum++;
                        }

                    }
                }
                if (uploadNum &gt; 0) {
                    photoStr += "\n手动上传(" + uploadNum + ")";
                }
                if (autoNum &gt; 0) {
                    photoStr += "\n自动采集(" + autoNum + ")";
                }
                if (photo360Num &gt; 0) {
                    photoStr += "\n全景影像(" + photo360Num + ")";
                }
                if (photoStr != "") {
                    photoStr = photoStr.substr(1);
                }
                newData.push(photoStr);
            }
        }
        //照片总数量
        newData.push(photoNumTotal + "");
        //状态
        newData.push(statusValue);

        datas.push(newData);
    }

    //前端页面表格列
    var gridCols = [];
    for (var i = 0; i &lt; newNames.length; i++) {
        gridCols.push({
            field: "V" + i,
            align: "center",
            title: newNames[i]
        });
    }

    //前端页面表格数据
    var gridDatas = [];
    for (var i = 0; i &lt; datas.length; i++) {
        var d = datas[i];
        var row = {};
        for (var j = 0; j &lt; d.length; j++) {
            row["V" + j] = d[j];
        }
        gridDatas.push(row);
    }

    datas.unshift(newNames);
    res.success = true;
    res.data = {
        excelData: datas,
        gridDatas: gridDatas,
        gridCols: [gridCols]
    };
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryQualityReportTpl">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryQualityReportTpl
 * @description   查询质量报告模板  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    nodeName    
 *
 * @returns    {JSON}
 */
var res = {};

try {
    var sql = "select * from quality_report_tpl where nodename='" + nodeName + "'";
    var rs = Things["Thing.DB.Oracle"].RunQuery({ sql: sql }).ToJSON().rows;
    if (rs.length == 0) {
        res.success = false;
        res.msg = "暂未上传质量报告模板！";
    } else {
        res.data = rs[0];
        res.success = true;
        res.msg = "";
    }
} catch (error) {
    res.success = false;
    res.msg = "查询质量报告模板出错，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryQualitySign">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryQualitySign
 * @description   查询质量数据签名 wanghq 2021年12月2日18:44:35
 * @implementation    {Script}
 *
 * @param    {INTEGER}    qualityPlanId    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};
try {
    var sql = "select * from quality_sign where quality_plan_id=" + qualityPlanId + " order by create_time desc";
    var rs = Things["Thing.DB.Oracle"].RunQuery({ sql: sql }).ToJSON().rows;
    res.data = rs;
} catch (error) {
    res.success = false;
    res.msg = "查询质量数据签名出错，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryQualitySummary">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryQualitySummary
 * @description   查询质量数据汇总表 wanghq 2021年12月28日14:24:05
 * @implementation    {Script}
 *
 * @param    {NUMBER}    tree_id    
 * @param    {INTEGER}    table_config_id    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};

var planResult = Things["Thing.DB.Oracle"].RunQuery({
    sql: "select * from quality_plan where type=1 and table_config_id=" + table_config_id + " and treeid=" + tree_id
});
if (planResult.rows.length == 0) {
    res.success = false;
    res.data = [];
    res.msg = "暂未上传策划表！";
} else {
    var params = Things["Thing.DB.Oracle"]
        .RunQuery({
            sql: "select * from param_config where table_id=" + table_config_id
        })
        .ToJSON().rows;

    //获取所有的去重的属性（一条数据的唯一标识）
    var onlyParams = [];
    //共有的名称数组
    var onlyNames = [];
    //共有的属性名称对应的字段名称
    var onlyColNames = [];

    for (var i = 0; i &lt; params.length; i++) {
        var param = params[i];
        if (param.IS_REMOVE_REPEAT == "1") {
            onlyParams.push(param);
            onlyNames.push(param.PARAM_NAME);
            onlyColNames.push(param.COL_NAME);
        }
    }

    //根据列的名称查询 数据库字段名称
    function getParamColName(paramName) {
        var colName = "";
        for (var i = 0; i &lt; params.length; i++) {
            var param = params[i];
            if (paramName == param["PARAM_NAME"]) {
                colName = param["COL_NAME"];
                break;
            }
        }
        return colName;
    }

    //该节点下的所有质量数据
    var tableDatas = me.QueryTableData({
        processTreeId: tree_id,
        table_config_id: table_config_id,
        type: "process",
        query: {
            queryUser: username
        }
    }).array;

    //序号列
    var indexColName = me.QueryTableSortCol({ tableConfigId: table_config_id }).indexColName;

    //从所有的质量数据中 筛选 与唯一标识相同的 数据
    function getTableData(onlyObj) {
        var row = undefined;
        for (var i = 0; i &lt; tableDatas.length; i++) {
            var tableData = tableDatas[i];
            var flag = true;
            for (var key in onlyObj) {
                if (onlyObj[key] != tableData[key]) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                row = tableData;
                break;
            }
        }
        return row;
    }
    var planData = JSON.parse(planResult.rows[0]["DATA"]);
    // 上传的策划表的表头
    var planParamNames = planData[0];
    //["V1","V2"...]
    var planColNames = [];
    for (var i = 0; i &lt; planParamNames.length; i++) {
        planColNames.push(getParamColName(planParamNames[i]));
    }

    var resData = [];
    for (var i = 1; i &lt; planData.length; i++) {
        var rowData = planData[i];
        var row = {};
        for (var j = 0; j &lt; rowData.length; j++) {
            row[planColNames[j]] = rowData[j];
        }
        var onlyObj = {};
        for (var j = 0; j &lt; onlyColNames.length; j++) {
            onlyObj[onlyColNames[j]] = row[onlyColNames[j]];
        }
        var tableData = getTableData(onlyObj);
        if (tableData != undefined) {
            for (var key in tableData) {
                if (row[key]) {
                    if (row[key] != tableData[key]) {
                        //策划的要求值与实际数据的要求值不匹配 已实际数据为准
                        row[key + "_unequal"] = row[key];
                        row[key] = tableData[key];
                    }
                } else {
                    row[key] = tableData[key];
                }
            }
        } else {
            for (var x = 0; x &lt; params.length; x++) {
                if (row[params[x]["COL_NAME"]]) {
                } else {
                    row[params[x]["COL_NAME"]] = "";
                }
            }
        }
        row["ROWNO"] = i;
        row["ROWNUM"] = i;
        if (indexColName) {
            row[indexColName] = i;
        }
        resData.push(row);
    }
    res.success = true;
    res.data = resData;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryStandAlone">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryStandAlone
 * @description   用于全景图中查询单机信息 wanghq 2023年10月23日14:06:57
 * @implementation    {Script}
 *
 * @param    {STRING}    info    
 * @param    {STRING}    queryUser    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var infos = info.split(" ");
    if (infos.length &gt;= 3) {
        var nameValue = infos[0], codeValue = infos[1], batchValue = infos[2];
        //查询单机配置表的信息
        var queryTableSql = "select * from TABLE_CONFIG where TREE_NAME = '单机拆装'";
        var table = Things['Thing.DB.Oracle'].RunQuery({ sql: queryTableSql }).rows[0];

        var tableName = table.TABLE_NAME;
        var tableId = table.ID;

        //查询参数信息
        var queryParamSql = "select * from PARAM_CONFIG where TABLE_ID=" + tableId;
        var params = Things['Thing.DB.Oracle'].RunQuery({ sql: queryParamSql }).rows;

        var nameColName, codeColName, batchColName;
        for (var i = 0; i &lt; params.length; i++) {
            var param = params[i];
            var colName = param['COL_NAME'];
            var interfaceName = param['INTERFACE_NAME'];
            if (interfaceName == 'OBJECT_NAME') {
                //名称
                nameColName = colName;
            } else if (interfaceName == 'OBJECT_CODE') {
                codeColName = colName;
            } else if (interfaceName == 'OBJECT_BATCH') {
                batchColName = colName;
            }
        }
        //查询具体的数据
        // var queryDataSql = "select * from " + tableName + " where " + nameColName + "='" + nameValue + "' and " + codeColName + "='" + codeValue + "' and " + batchColName + "='" + batchValue + "'";
        var queryDataSql = me.GetTableDataSql({
            table_config_id: tableId,
            query: {
                params: [{
                    name: nameColName,
                    value: nameValue
                }, {
                    name: codeColName,
                    value: codeValue
                }, {
                    name: batchColName,
                    value: batchValue
                }],
                queryUser:queryUser
            }
        });

        var datas = Things['Thing.DB.Oracle'].RunQuery({ sql: queryDataSql }).ToJSON().rows;

        function getParamValue(data, colName) {
            var value = "";
            for (var key in data) {
                if (key == colName) {
                    value = data[key];
                    break;
                }
            }
            return value;
        }

        function getRelationParam(relationId, data) {
            var relationParam = {
                paramValue: "",
                paramName: ""
            };
            for (var i = 0; i &lt; params.length; i++) {
                var param = params[i];
                if (relationId == param['ID']) {
                    var paramValue = getParamValue(data, param['COL_NAME']);
                    relationParam = {
                        paramValue: paramValue,
                        paramName: param['PARAM_NAME']
                    };
                    break;
                }
            }
            return relationParam;
        }

        /**
         * 对基础属性进行排序 优先 名称 、代号 、要求批次号
         */
        function sortBaseParams(baseParamObjs) {
            // 定义排序顺序
            var sortOrder = ["名称", "代号", "要求批次号", "实际批次号"];

            // 使用Array.sort()方法进行排序
            baseParamObjs.sort(function (a, b) {
                var indexA = sortOrder.indexOf(a.paramName);
                var indexB = sortOrder.indexOf(b.paramName);

                // 如果paramName不在排序顺序内，按照默认顺序排序
                if (indexA === -1) {
                    indexA = sortOrder.length;
                }
                if (indexB === -1) {
                    indexB = sortOrder.length;
                }
                // 比较排序索引
                return indexA - indexB;
            });
        }

        if (datas.length &gt; 0) {
            var data = datas[0];
            var baseParamObjs = [];//基本属性的集合
            var testParamObjs = [];//设计值和实测值的集合
            for (var i = 0; i &lt; params.length; i++) {
                var param = params[i];

                if (param.FORMAT != 1 &amp;&amp; param.IS_SHOW_IN_360 == 1) {
                    var paramName = param['PARAM_NAME'];
                    var colName = param['COL_NAME'];
                    var paramValue = getParamValue(data, colName);
                    if (param['VALUE_TYPE'] == 0) {
                        baseParamObjs.push({
                            paramName: paramName,
                            paramValue: paramValue
                        });
                    } else if (param['VALUE_TYPE'] == 1) {
                        var relationId = param['RELATION_PARAM'];
                        testParamObjs.push({
                            paramName: paramName,
                            paramValue: paramValue,
                            relationParam: getRelationParam(relationId, data)
                        });
                    }
                }
            }


            sortBaseParams(baseParamObjs);
            res.success = true;
            res.data = {
                baseParamObjs: baseParamObjs,
                testParamObjs: testParamObjs
            };
            res.msg = "成功";

        } else {
            res.success = false;
            res.msg = "未查询到数据！";
        }

    } else {
        res.success = false;
        res.msg = "单机参数不全，请检查！";
    }

} catch (error) {
    res.success = false;
    var msg = "QueryStandAlone-失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryStandAloneByModel">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**&#xD;
* @definition    QueryStandAloneByModel&#xD;
* @description   用于全景图中查询单机信息 wanghq 2025年6月9日14:06:57&#xD;
* @implementation    {Script}&#xD;
*&#xD;
* @param    {NUMBER}    modelId    &#xD;
*&#xD;
* @returns    {JSON}&#xD;
*/&#xD;
var res = {};&#xD;
try {&#xD;
    logger.info("QueryStandAloneByModel - 开始查询单机信息，modelId: " + modelId);&#xD;
&#xD;
    //查询单机配置表的信息&#xD;
    var queryTableSql = "select * from TABLE_CONFIG where TREE_NAME = '单机拆装'";&#xD;
    logger.debug("QueryStandAloneByModel - 查询表配置SQL: " + queryTableSql);&#xD;
    var table = Things['Thing.DB.Oracle'].RunQuery({ sql: queryTableSql }).rows[0];&#xD;
&#xD;
    var tableName = table.TABLE_NAME;&#xD;
    var tableId = table.ID;&#xD;
    logger.info("QueryStandAloneByModel - 使用表名: " + tableName + ", 表ID: " + tableId);&#xD;
&#xD;
    //查询参数信息&#xD;
    var queryParamSql = "select * from PARAM_CONFIG where TABLE_ID=" + tableId;&#xD;
    logger.debug("QueryStandAloneByModel - 查询参数配置SQL: " + queryParamSql);&#xD;
    var params = Things['Thing.DB.Oracle'].RunQuery({ sql: queryParamSql }).rows;&#xD;
    logger.debug("QueryStandAloneByModel - 查询到参数配置数量: " + params.length);&#xD;
&#xD;
    var nameColName, codeColName, batchColName;&#xD;
    for (var i = 0; i &lt; params.length; i++) {&#xD;
        var param = params[i];&#xD;
        var colName = param['COL_NAME'];&#xD;
        var interfaceName = param['INTERFACE_NAME'];&#xD;
        logger.debug("QueryStandAloneByModel - 处理参数配置: colName=" + colName + ", interfaceName=" + interfaceName);&#xD;
        if (interfaceName == 'OBJECT_NAME') {&#xD;
            //名称&#xD;
            nameColName = colName;&#xD;
            logger.debug("QueryStandAloneByModel - 设置名称列名: " + nameColName);&#xD;
        } else if (interfaceName == 'OBJECT_CODE') {&#xD;
            codeColName = colName;&#xD;
            logger.debug("QueryStandAloneByModel - 设置代码列名: " + codeColName);&#xD;
        } else if (interfaceName == 'OBJECT_BATCH') {&#xD;
            batchColName = colName;&#xD;
            logger.debug("QueryStandAloneByModel - 设置批次列名: " + batchColName);&#xD;
        }&#xD;
    }&#xD;
&#xD;
    logger.info("QueryStandAloneByModel - 列名映射完成: nameColName=" + nameColName + ", codeColName=" + codeColName + ", batchColName=" + batchColName);&#xD;
&#xD;
    var querySql = "select * from " + tableName + " where TREE_ID in (select treeid from datapackagetree start with treeid = " + modelId + " connect by prior treeid = parentid)";&#xD;
    logger.debug("QueryStandAloneByModel - 查询单机数据SQL: " + querySql);&#xD;
    var datas = Things['Thing.DB.Oracle'].RunQuery({ sql: querySql }).ToJSON().rows;&#xD;
    logger.info("QueryStandAloneByModel - 查询到单机数据数量: " + datas.length);&#xD;
&#xD;
    var resData = [];&#xD;
    for (var i = 0; i &lt; datas.length; i++) {&#xD;
        var data = datas[i];&#xD;
        var deviceName = data[nameColName];&#xD;
        var deviceCode = data[codeColName];&#xD;
        var deviceBatch = data[batchColName];&#xD;
        logger.debug("QueryStandAloneByModel - 处理单机数据[" + i + "]: deviceName=" + deviceName + ", deviceCode=" + deviceCode + ", deviceBatch=" + deviceBatch);&#xD;
        resData.push({&#xD;
            devicename: deviceName,&#xD;
            devicecode: deviceCode,&#xD;
            devicebatch: deviceBatch&#xD;
        });&#xD;
    }&#xD;
&#xD;
    logger.info("QueryStandAloneByModel - 数据处理完成，返回设备数量: " + resData.length);&#xD;
&#xD;
    res.success = true;&#xD;
    res.data = resData;&#xD;
    res.msg = "QueryStandAloneByModel-查询单机信息成功";&#xD;
    &#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    res.msg = "QueryStandAloneByModel-查询单机信息失败，原因：" + error;&#xD;
    logger.error("QueryStandAloneByModel - " + res.msg);&#xD;
    logger.error("QueryStandAloneByModel - 错误堆栈: " + error.stack);&#xD;
}&#xD;
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTable
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    tree_name    
 *
 * @returns    {INFOTABLE}
 */
var sql1 = "select table_id from relation_table where nodename='"+tree_name+"'";

var rs1 = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql1 /* STRING */
});
if(rs1.rows.length&gt;0){	
    if(rs1.rows[0].TABLE_ID){
    	var tableIds = rs1.rows[0].TABLE_ID;
        var sql = "select * from TABLE_CONFIG where ID in ("+tableIds+") order by create_time";
        var result = Things['Thing.DB.Oracle'].RunQuery({
            sql: sql /* STRING */
        });
    } 
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTableById">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTableById
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    tableId    
 *
 * @returns    {INFOTABLE}
 */
var sql = "select * from TABLE_CONFIG where ID='"+tableId+"'";
var result = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTableByNodename">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTableByNodename
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    nodename    
 *
 * @returns    {INFOTABLE}
 */
var sql1 = "select table_id from relation_table where nodename='"+nodename+"'";

var rs1 = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql1 /* STRING */
});
if(rs1.rows.length&gt;0){	
    if(rs1.rows[0].TABLE_ID){
    	var tableIds = rs1.rows[0].TABLE_ID;
        var sql = "select * from TABLE_CONFIG where ID in ("+tableIds+") order by create_time";
        var result = Things['Thing.DB.Oracle'].RunQuery({
            sql: sql /* STRING */
        });
    } 
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTableComponent">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTableComponent
 * @description   查询电缆网元器件数据 合格证相同的产品的实际装机数量要累加 wanghq 2022年4月22日15:59:25
 * @implementation    {Script}
 *
 * @param    {NUMBER}    processTreeId    
 * @param    {NUMBER}    productTreeId    
 * @param    {INTEGER}    table_config_id    
 * @param    {JSON}    query    
 *
 * @returns    {JSON}
 */
var resData = [];

var sql = me.GetTableDataSql({
    processTreeId: processTreeId,
    productTreeId: productTreeId,
    table_config_id: table_config_id,
    query: query
});
var rs = Things["Thing.DB.Oracle"].RunQuery({
    sql: sql
}).ToJSON();

if (rs.rows.length &gt; 0) {
    var fieldDefinitions = rs.dataShape.fieldDefinitions;
    var rows = rs.rows;
    for (var field in fieldDefinitions) {
        for (var i = 0; i &lt; rows.length; i++) {
            if (rows[i][field]) {
            } else {
                rows[i][field] = "";
            }
        }
    }
}

//合格证、批次号、规格的字段名称 
var certificateColumn = "", batchColumn = "", specColumn = "";
//数量的字段名称
var numColunm = "";

// 对应的属性表中的 序号列、排序列和去重列
var sortJson = me.QueryTableSortCol({ tableConfigId: table_config_id });

//序号列 可能存在多个
var indexColNames = sortJson.indexColName;
var indexColNameArr = indexColNames.split(",");

var params = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from param_config where table_id=" + table_config_id }).rows;
for (var i = 0; i &lt; params.length; i++) {
    var param = params[i];
    if (param.PARAM_NAME == '所内合格证') {
        certificateColumn = param.COL_NAME;
    }
    if (param.PARAM_NAME == '批次号') {
        batchColumn = param.COL_NAME;
    }
    if (param.PARAM_NAME == '规格') {
        specColumn = param.COL_NAME;
    }
    if (param.PARAM_NAME.indexOf('实际装机数量') &gt; -1) {
        numColunm = param.COL_NAME;
    }
}
if (certificateColumn != "" &amp;&amp; numColunm != "") {



    //去重所内合格证+批次号+规格
    var distinctList = Resources["InfoTableFunctions"].Distinct({
        t: rs,
        columns: certificateColumn + ',' + batchColumn + ',' + specColumn
    });
    //创建合格证对象
    var certificateObj = {};
    for (var i = 0; i &lt; distinctList.rows.length; i++) {
        var certificate = distinctList.rows[i][certificateColumn];
        var batch = distinctList.rows[i][batchColumn];
        var spec = distinctList.rows[i][specColumn];
        certificateObj[certificate + "_" + batch + "_" + spec] = [];
    }
    for (var i = 0; i &lt; rs.rows.length; i++) {
        var row = rs.rows[i];
        var certificate = row[certificateColumn];
        var batch = row[batchColumn];
        var spec = row[specColumn];
        certificateObj[certificate + "_" + batch + "_" + spec].push(JSON.parse(JSON.stringify(row)));
    }
    var dataIndex = 0;
    for (var certificate in certificateObj) {
        var certificateData = certificateObj[certificate];
        if (certificateData.length &gt; 0) {
            dataIndex++;
            var certificateRow = certificateData[0];
            //计算实际装机数量
            var numTotal = 0;
            var unit = "";//单位
            for (var x = 0; x &lt; certificateData.length; x++) {
                var row = certificateData[x];
                //实际装机数量
                var num = row[numColunm] || '0';
                unit = unit == "" ? num.replace(/\d+/, "") : unit;
                numTotal += parseInt(num);
            }
            var finalNum = numTotal + unit;
            certificateRow[numColunm] = finalNum;

            if (indexColNames != "") {
                for (var i = 0; i &lt; indexColNameArr.length; i++) {
                    certificateRow[indexColNameArr[i]] = dataIndex;
                }
            }
            resData.push(certificateRow);
        }
    }
}
result = resData;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTableData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTableData
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    processTreeId    
 * @param    {NUMBER}    table_config_id    
 * @param    {INTEGER}    productTreeId        {"aspect.defaultValue":"-1"}
 * @param    {INTEGER}    dlw_is_all        {"aspect.defaultValue":"2"}
 * @param    {JSON}    query    
 * @param    {STRING}    table_config_name        {"aspect.defaultValue":"undefined"}
 *
 * @returns    {JSON}
 */
var resData = [];
var table = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from table_config where id=" + table_config_id });
if (table_config_name == 'undefined') {
    table_config_name = table.rows[0]['TREE_NAME'];
}
if (table_config_name == '电缆网元器件汇总表' &amp;&amp; dlw_is_all == 2) {
    resData = me.QueryTableComponent({
        processTreeId: processTreeId,
        productTreeId: productTreeId,
        table_config_id: table_config_id,
        query:query
    });
} else {
    var sql = me.GetTableDataSql({
        processTreeId: processTreeId,
        productTreeId: productTreeId,
        table_config_id: table_config_id,
        query:query
    });
    logger.error('QueryTableData-sql:'+sql);
    var rs = Things["Thing.DB.Oracle"].RunQuery({
        sql: sql
    });
    if (rs.rows.length &gt; 0) {
        var json = rs.ToJSON();
        var fieldDefinitions = json.dataShape.fieldDefinitions;
        var rows = json.rows;
        for (var field in fieldDefinitions) {
            for (var i = 0; i &lt; rows.length; i++) {
                if (rows[i][field]) {
                } else {
                    rows[i][field] = "";
                }
            }
        }
        resData = rows;
    }
}
result = resData;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTableDataCount">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTableDataCount
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    processTreeId        {"aspect.defaultValue":"-1"}
 * @param    {INTEGER}    table_config_id    
 * @param    {NUMBER}    productTreeId    
 * @param    {JSON}    query    
 * @param    {STRING}    table_config_name        {"aspect.defaultValue":"undefined"}
 *
 * @returns    {INTEGER}
 */
var res = 0;
var table = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from table_config where id=" + table_config_id });
if (table_config_name == 'undefined') {
    table_config_name = table.rows[0]['TREE_NAME'];
}
if (table_config_name == '电缆网元器件汇总表') {
    var componentData = me.QueryTableComponent({
        processTreeId: processTreeId,
        productTreeId: productTreeId,
        table_config_id: table_config_id,
        query:query
    });
    res = componentData.array.length;
} else {
    var selectSql = me.GetTableDataSql({
        processTreeId: processTreeId,
        productTreeId: productTreeId,
        table_config_id: table_config_id,
        query:query
    });
    var sql = "select count(*) count from (" + selectSql + ")";
    var rs = Things["Thing.DB.Oracle"].RunQuery({
        sql: sql
    });
    res = rs.rows[0]["COUNT"];
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTableDataPage">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTableDataPage
 * @description   分页查询二级表数据  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    processTreeId        {"aspect.defaultValue":"-1"}
 * @param    {INTEGER}    table_config_id    
 * @param    {NUMBER}    productTreeId    
 * @param    {INTEGER}    pageNumber    
 * @param    {INTEGER}    pageSize    
 * @param    {JSON}    query    
 * @param    {STRING}    table_config_name        {"aspect.defaultValue":"undefined"}
 *
 * @returns    {JSON}
 */
var startRowno = (pageNumber - 1) * pageSize + 1;
var endRowno = pageNumber * pageSize;

var resData = [];
var table = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from table_config where id=" + table_config_id });
if (table_config_name == 'undefined') {
    table_config_name = table.rows[0]['TREE_NAME'];
}
if (table_config_name == '电缆网元器件汇总表') {
    var componentData = me.QueryTableComponent({
        processTreeId: processTreeId,
        productTreeId: productTreeId,
        table_config_id: table_config_id,
        query: query
    });
    resData = componentData.array.slice(startRowno - 1, endRowno);
} else {
    var selectSql = me.GetTableDataSql({
        processTreeId: processTreeId,
        productTreeId: productTreeId,
        table_config_id: table_config_id,
        query: query
    });
    var queryDataSql = "select * from (" + selectSql + ") where rowno &gt;= " + startRowno + " and rowno &lt;=" + endRowno;
    var rs = Things["Thing.DB.Oracle"].RunQuery({
        sql: queryDataSql /* STRING */
    });
    if (table_config_name == '热管') {
        //抽取产品编号中的编号，规则是取最后一个"-"后面的内容
        function extractCodeNumber(code) {
            if (!code) {
                return "";
            }

            var lastDashIndex = code.lastIndexOf("-");
            if (lastDashIndex === -1) {
                return "";
            }

            var result = code.substring(lastDashIndex + 1);
            return result;
        }

        function queryAllRgPhoto() {
            var rgPhotoSql = "select FILE_NAME,FILE_FORMAT,FILEPATH,FILENAME\
                                from PROCESS_CONTROL_RESULT\
                                where NODECODE = (select id from DATA_PACKAGE where REFTREEID = " + processTreeId + ")\
                                and FILE_TYPE = '影像记录'\
                                and FILE_NAME like 'sync-%'\
                                and GATHERING_METHOD = '手动采集'";
            var rgPhotos = Things['Thing.DB.Oracle'].RunQuery({ sql: rgPhotoSql }).rows;
            return rgPhotos;
        }


        //处理热管照片
        var rgParamSql = "select * from PARAM_CONFIG where TABLE_ID= (select ID from TABLE_CONFIG where Tree_NAME='热管')";
        var rgParams = Things['Thing.DB.Oracle'].RunQuery({ sql: rgParamSql }).rows;
        var codeParam, rgPhotoParams = [];
        for (var i = 0; i &lt; rgParams.length; i++) {
            var param = rgParams[i];
            if (param['PARAM_NAME'] == '产品编号') {
                codeParam = param;
            }
            if (param['FORMAT'] == '8') {
                rgPhotoParams.push(param);
            }
        }
        //定义数据分隔符
        var dataSeparator = "!@#$%^";
        //定义文件路径与名称之间的分隔符
        var fileSeparator = "^%$#@!";
        if (codeParam &amp;&amp; rgPhotoParams.length &gt; 0) {
            var rgPhotos = queryAllRgPhoto();
            var codeColName = codeParam['COL_NAME'];
            for (var i = 0; i &lt; rs.rows.length; i++) {
                var row = rs.rows[i];
                var code = row[codeColName]; //产品编号 XX-01-89
                var codeNumber = extractCodeNumber(code);
                if (codeNumber) {
                    for (var j = 0; j &lt; rgPhotoParams.length; j++) {
                        var rgPhotoParam = rgPhotoParams[j];
                        var rgPhotoColName = rgPhotoParam['COL_NAME'];
                        for (var k = 0; k &lt; rgPhotos.length; k++) {
                            var rgPhoto = rgPhotos[k];
                            var rgPhotoFileName = rgPhoto['FILE_NAME'];
                            var rgPhotoFilePath = rgPhoto['FILEPATH'];
                            var codes = rgPhoto['FILENAME'];//照片编号，格式为12,13,14
                            var codesArray = codes.split(',');
                            for (var l = 0; l &lt; codesArray.length; l++) {
                                var code = codesArray[l];
                                if (code == codeNumber) {
                                    row[rgPhotoColName] = row[rgPhotoColName] + dataSeparator + rgPhotoFilePath + fileSeparator + rgPhotoFileName;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    //以下代码处理超差报警操作

    //查询该类型下的所有为设计值的属性
    var queryParamSql = "select a.*, b.COL_NAME as relation_col_name\
                             from (select id, COL_NAME, RELATION_PARAM from PARAM_CONFIG where TABLE_ID = "+ table_config_id + " and VALUE_TYPE = 1) a\
                             left join PARAM_CONFIG b on a.RELATION_PARAM = b.id";

    var params = Things['Thing.DB.Oracle'].RunQuery({ sql: queryParamSql });

    function getRelationField(field) {
        var res = undefined;
        for (var i = 0; i &lt; params.length; i++) {
            var param = params[i];
            if (param['COL_NAME'] == field) {
                if (param['RELATION_COL_NAME']) {
                    res = param['RELATION_COL_NAME'];
                }
                break;
            }
        }
        return res;
    }
    if (rs.rows.length &gt; 0) {
        var json = rs.ToJSON();
        var fieldDefinitions = json.dataShape.fieldDefinitions;
        var rows = json.rows;
        for (var field in fieldDefinitions) {
            //设计值字段对应的实际值字段
            var actField = getRelationField(field);
            for (var i = 0; i &lt; rows.length; i++) {
                if (rows[i][field]) {
                    if (rows[i]['STATUS'] != '1') {
                        if (actField != undefined) {
                            //存在实测值字段的时候才做比较
                            var designValue = rows[i][field];
                            var actualValue = rows[i][actField];
                            actualValue = String(actualValue) == "undefined" ? "" : String(actualValue);
                            var flag = me.CompareValue({
                                designStringValue: designValue,
                                actualStringValue: actualValue
                            });
                            if (!flag) {
                                // rows[i][actField] = '&lt;span style="bcakground-color:red;"&gt;' + actualValue + '&lt;span&gt;';
                                rows[i][actField] = actualValue + "-~!-false";
                            }
                        }
                    }
                } else {
                    //将数据库中值为空的字段设置为空字符串
                    rows[i][field] = "";
                }
            }
        }
        resData = rows;
    }
}
result = resData;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTableSortCol">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTableSortCol
 * @description   查询表格的排序列和去重列  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableConfigId    
 *
 * @returns    {JSON}
 */
/**
 * QueryTableSortCol Local (JavaScript)
 *
 * 获取二级表的去重列、排序列和序号列
 *
 * tableConfigId INTEGER
 *
 * return JSON
 */
var sql = "select * from PARAM_CONFIG where TABLE_ID = " + tableConfigId;
var rs = Things["Thing.DB.Oracle"].RunQuery({
    sql: sql /* STRING */
});
var result = {};
var indexColNames = [];
var sortColNames = [];
var repeatColNames = [];
for (var i = 0; i &lt; rs.getRowCount(); i++) {
    var row = rs.rows[i];
    var format = row.FORMAT;
    if (format == "1") {
        indexColNames.push(row.COL_NAME);
    }
    var secondArea = row.SECOND_AREA; //序号的二级表单元格位置
    var isCapital = me.isCapital({
        str: secondArea /* STRING */
    });
    if (isCapital) {
        if (row.IS_SORT == 1) {
            sortColNames.push({
                colName: row.COL_NAME,
                paramName: row.PARAM_NAME
            });
        }
        if (row.IS_REMOVE_REPEAT == 1) {
            repeatColNames.push(row.COL_NAME);
        }
    }
}
result.indexColName = indexColNames.join(",");
result.sort = sortColNames;
result.repeat = repeatColNames.join(",");</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTdPhoto">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTdPhoto
 * @description   查询单元格的照片信息 wanghq 2022年12月6日23:06:38
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    onlyValue    
 * @param    {NUMBER}    paramId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var onlyObj = JSON.parse(onlyValue);

    var table = me.QueryTableById({ tableId: tableConfigId }).rows[0];
    //MES接口标识
    var mesInterface = table["MES_INTERFACE"];

    var params = me.QueryParamsById({ tableId: tableConfigId }).ToJSON().rows;

    //获取所有的去重的属性（一条数据的唯一标识）
    var onlyParams = [];
    for (var i = 0; i &lt; params.length; i++) {
        var param = params[i];
        if (param.IS_REMOVE_REPEAT == "1") {
            onlyParams.push(param);
        }
    }


    var majorNodeSql = "select TREEID" +
        " from DATAPACKAGETREE" +
        " start with TREEID = (" +
        "select TREEID from DATAPACKAGETREE where NODETYPE = 'dir' start with TREEID = " + treeId + " connect by prior PARENTID = TREEID)" +
        " connect by prior TREEID = PARENTID";
    //查询该过程上一层节点下的所有从mes系统采集过来的质量影像记录
    var allAutoPhotoSql = "select OBJECT_CODE,OBJECT_TYPE,OBJECT_BATCH,OBJECT_NAME,FILEPATH from (select * from (select ID, FILE_NAME, TABLENAME, FILEPATH from RESULTGATHER a where FILE_TYPE = '影像记录' " +
        "and GATHERING_METHOD = '自动采集' and NODECODE in (select ID from DATA_PACKAGE where REFTREEID in (" + majorNodeSql + "))) " +
        "a left join (select RESULT_ID, PHOTONAME, DOWNLOADURL, SOURCE_ID, OBJECT_TYPE, OBJECT_BATCH, OBJECT_CODE, OBJECT_NAME from "
        + "XMLDATA_PHOTO) b on a.ID = b.RESULT_ID and b.SOURCE_ID like '%' || a.TABLENAME || '%') s where s.OBJECT_TYPE = '" + mesInterface + "'";
    var allAutoPhotos = Things['Thing.DB.Oracle'].RunQuery({ sql: allAutoPhotoSql }).ToJSON().rows;


    function dealPrefix(sort, num) {
        if (!num) {
            num = 2;
        }
        var sortStr = sort + "";
        if (sortStr.length &lt; num) {
            var temp = "";
            for (var i = 0; i &lt; (num - sortStr.length); i++) {
                temp += "0";
            }
            sortStr = temp + sort;
        }
        return sortStr;
    }

    /**
     * 
     * @param {*} onlyObj 唯一标识对象
     * 
     * @returns {*} 替换唯一标识key值为MES接口的key
     */
    function dealOnlyObj2MesObj() {
        var newObj = {};
        for (var key in onlyObj) {
            for (var x = 0; x &lt; onlyParams.length; x++) {
                var colName = onlyParams[x]['COL_NAME'];
                if (key == colName) {
                    var interfaceName = onlyParams[x]['INTERFACE_NAME'];
                    if (interfaceName) {
                        newObj[interfaceName] = onlyObj[key];
                    }
                    break;
                }
            }
        }
        return newObj;
    }
    var newObj = dealOnlyObj2MesObj();


    /**
    *  在手动的照片基础上添加自动同步的照片
    * @param {*} photo 手动上传的照片
    * 
    */
    function getAutoPhoto(photo) {
        var autoPhotos = [];
        for (var x = 0; x &lt; allAutoPhotos.length; x++) {
            var autoPhoto = allAutoPhotos[x];
            var flag = true;
            for (var key in newObj) {
                if (autoPhoto[key] !== newObj[key]) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                autoPhotos.push(autoPhoto);
            }
        }

        var nameArr = [];
        for (var key in onlyObj) {
            nameArr.push(onlyObj[key]);
        }
        var names = nameArr.join("_");

        for (var x = 0; x &lt; autoPhotos.length; x++) {
            var autoPhoto = autoPhotos[x];
            autoPhoto['TYPE'] = 'auto';
            autoPhoto['PHOTO_PATH'] = autoPhoto['FILEPATH'];
            var num = names + '-' + dealPrefix(x + 1, 3) + '(自动)';
            autoPhoto['PHOTO_NUMBER'] = num;
        }
        if (autoPhotos.length &gt; 0) {
            photo = photo.concat(autoPhotos);
        }
        return photo;
    }


    //查询该过程节点下的所有手动上传的质量影像记录
    var allPhotoDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: "select PHOTO_NUMBER,PHOTO_FORMAT,PHOTO_PATH,ID,ONLY_VALUES,PARAM_ID from QUALITY_SINGLE_PHOTO where tree_id=" + treeId + " and TABLE_CONFIG_ID=" + tableConfigId + " order by PHOTO_NUMBER" }).ToJSON().rows;
    //根据唯一标识和属性ID获取照片
    function getPhoto() {
        var photo = [];
        for (var i = 0; i &lt; allPhotoDatas.length; i++) {
            var t = allPhotoDatas[i];
            var onlyValues = JSON.parse(t["ONLY_VALUES"]);
            var tFlag = true;
            for (var key in onlyObj) {
                if (onlyObj[key] !== onlyValues[key]) {
                    tFlag = false;
                    break;
                }
            }
            if (tFlag &amp;&amp; t["PARAM_ID"] == paramId) {
                t["TYPE"] = "upload";
                photo.push(t);
            }
        }
        if (onlyParams.length == 0) {
            return photo;
        }
        return getAutoPhoto(photo);
    }
    var myPhotos = getPhoto();
    res.success = true;
    res.data = JSON.stringify(myPhotos);
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "查询单元格的照片信息失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTemplateTree">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTemplateTree
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {INFOTABLE}
 */
var sql ="select * from DATAPACKAGETREE s start with s.TREEID=3 CONNECT by  PRIOR s.TREEID = s.PARENTID";
var result = Things['Thing.DB.Oracle'].RunQuery({sql:sql});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTreeNodeByName">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTreeNodeByName
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    id        {"aspect.isRequired":"true"}
 *
 * @returns    {INFOTABLE}
 */
//QueryTreeNodeByName Local (JavaScript)
//id STING  表格配置表的TABLE_ID

var sql = "select * from DATAPACKAGETREE where NODENAME in(select NODENAME from RELATION_TABLE where TABLE_ID like '%," + id + "%' or TABLE_ID like '%" + id + ",%' or TABLE_ID = '" + id + "')";
var result = Things["Thing.DB.Oracle"].RunQuery({
    sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryTreesByTableId">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryTreesByTableId
 * @description   通过table_config的id查询关联的树节点  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableId    
 *
 * @returns    {STRING}
 */
var data = {
    product: [],
    phase: [],
    dir: [],
    leaf: []
};
var minNodesSql = "select * from DATAPACKAGETREE where NODENAME in(select NODENAME from RELATION_TABLE where TABLE_ID like '%," + tableId + "%' or TABLE_ID like '%" + tableId + ",%' or TABLE_ID = '" + tableId + "')";

var minNodes = Things["Thing.DB.Oracle"]
    .RunQuery({
        sql: minNodesSql /* STRING */
    })
    .ToJSON();

function inArray(arr, node) {
    var flag = false;
    for (var i = 0; i &lt; arr.length; i++) {
        if (arr[i]["TREEID"] == node["TREEID"]) {
            flag = true;
            break;
        }
    }
    return flag;
}
for (var i = 0; i &lt; minNodes.rows.length; i++) {
    var minNode = minNodes.rows[i];
    var minNodeTreeId = minNode["TREEID"];
    var allNodes = Things["Thing.Fn.DataSearch"]
        .QueryTreeIdsByTreeId({
            treeId: minNodeTreeId
        })
        .ToJSON();
    for (var x = 0; x &lt; allNodes.rows.length; x++) {
        var node = allNodes.rows[x];
        var nodeType = node["NODETYPE"];
        var nodeTreeId = node["TREEID"];
        if (nodeType != "root" &amp;&amp; nodeType != "folder") {
            if (!inArray(data[nodeType], node)) {
                data[nodeType].push(node);
            }
        }
    }
}
result = JSON.stringify(data);</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="QueryType4TableData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    QueryType4TableData
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    tree_id    
 * @param    {STRING}    table_config_id    
 *
 * @returns    {JSON}
 */
//QueryType4TableData Local (JavaScript)
//tree_id STRING
//table_config_id STRING
var sql = "select ROWNUM,t.* from (select * from TABLE_DATA where table_config_id='" +
	table_config_id + "' and tree_id in (select TREEID from DATAPACKAGETREE s start with s.TREEID='" + tree_id + "' CONNECT by  PRIOR s.TREEID = s.PARENTID) order by ID) t";

var rs = Things['Thing.DB.Oracle'].RunQuery({
	sql: sql /* STRING */
});
var result = [];
for (var i = 0; i &lt; rs.getRowCount(); i++) {
	var row = rs.rows[i];
	var data = "[]";
	if (row.DATA) {
		data = row.DATA;
	}
	var filepath = row.FILEPATH;
	var filename = row.FILENAME;
    var treeId = parseInt(row.TREE_ID).toString();
	data = JSON.parse(data);
	for (var x = 0; x &lt; data.length; x++) {
		var d = data[x];
		d.filepath = filepath;
		d.filename = filename;
        d.treeId = treeId;
		d.time = row.CREATE_TIME;
		d.id = parseInt(row.ID).toString();
		result.push(d);
	}
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ReassociationData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ReassociationData
 * @description   重新关联质量数据  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    dataIds    
 * @param    {INTEGER}    tableId    
 * @param    {NUMBER}    newTreeId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var table = me.QueryTableById({
        tableId: tableId
    });
    var tableName = table.rows[0]["TABLE_NAME"];

    // 对应的属性表中的 序号列、排序列和去重列
    var sortJson = me.QueryTableSortCol({ tableConfigId: tableId });

    if (sortJson.repeat != "") {
        //如果存在去重更新的属性 则需要将去重属性值相同的数据一并关联
        var repeat = sortJson.repeat;
        var repeats = repeat.split(",");
        var repeatArr = [];
        var repeatSql = "";
        for (var i = 0; i &lt; repeats.length; i++) {
            repeatArr.push(repeats[i]);
            if (i == repeats.length - 1) {
                repeatSql += " " + repeats[i];
            } else {
                repeatSql += " " + repeats[i] + ",";
            }
        }
        //先查询ID中的去重属性
        var selectSql = "select * from " + tableName + " where id in(" + dataIds + ")";
        var selDatas = Things['Thing.DB.Oracle'].RunQuery({ sql: selectSql });
        for (var i = 0; i &lt; selDatas.rows.length; i++) {
            var data = selDatas.rows[i];
            var updateSql = "update " + tableName + " set tree_id=" + newTreeId + " where tree_id=" + treeId + "";
            for (var j = 0; j &lt; repeatArr.length; j++) {
                var repeatCol = repeatArr[j];
                if (data[repeatCol]) {
                    updateSql += " and " + repeatCol + "='" + data[repeatCol] + "'";
                } else {
                    updateSql += " and " + repeatCol + " is null";
                }
            }
            logger.error('updateSql:' + updateSql);
            Things['Thing.DB.Oracle'].RunCommand({ sql: updateSql });
        }
    } else {
        Things['Thing.DB.Oracle'].RunCommand({ sql: "update " + tableName + " set tree_id=" + newTreeId + " where id in(" + dataIds + ")" });
    }

    res.success = true;
    res.data = [];
    res.msg = "关联成功";
} catch (error) {
    res.success = false;
    res.msg = "关联失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="RecordDownloadFile">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    RecordDownloadFile
 * @description   记录下载在下载列表中的文件 datetime 2023年8月7日21:02:26
 * @implementation    {Script}
 *
 * @param    {NUMBER}    downloadId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var sql = "update QUALITY_DOWNLOAD set is_download=1 where id=" + downloadId;
    Things['Thing.DB.Oracle'].RunCommand({ sql: sql });
    res.success = true;
    res.data = [];
    res.msg = "记录下载在下载列表中的文件成功";
} catch (error) {
    res.success = false;
    var msg = "RecordDownloadFile-记录下载在下载列表中的文件失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="RelationBomTree">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    RelationBomTree
 * @description   将质量数据关联到产品结构树节点上  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    tableId    
 * @param    {NUMBER}    bomTreeId    
 * @param    {STRING}    dataIds    
 *
 * @returns    {NUMBER}
 */
var table = me.QueryTableById({
    tableId: tableId
});
var tableName = table.rows[0]["TABLE_NAME"];

var updateSql = "update " + tableName + " set PRODUCT_ID=" + bomTreeId + " where id in(" + dataIds + ")";
result = Things["Thing.DB.Oracle"].RunCommand({
    sql: updateSql
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="RelationTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    RelationTable
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    ids    
 * @param    {STRING}    nodename    
 * @param    {STRING}    creator    
 *
 * @returns    {NUMBER}
 */
var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
var delSql = "delete from RELATION_TABLE where NODENAME='"+nodename+"'";
Things['Thing.DB.Oracle'].RunCommand({
	sql: delSql /* STRING */
});
var sql = "insert into RELATION_TABLE (ID, NODENAME, TABLE_ID, CREATOR, CREATE_TIME)"+
"values (RELATION_TABLE_SEQ.nextval,'"+nodename+
    "', '"+ids+
    "', '"+creator+
    "', '"+now+
    "')";
logger.error(sql);
result = Things['Thing.DB.Oracle'].RunCommand({
	sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ReqGenerateFile">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ReqGenerateFile    {"aspect.isAsync":"true"}
 * @description   发起生成文件请求 datetime 2023年8月7日16:56:24
 * @implementation    {Script}
 *
 * @param    {NUMBER}    tableConfigId    
 * @param    {NUMBER}    treeId    
 * @param    {NUMBER}    exportType    
 * @param    {STRING}    creator    
 * @param    {NUMBER}    type_    
 * @param    {STRING}    fileName    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");

    var downloadId = Things['Thing.DB.Oracle'].RunQuery({ sql: "select QUALITY_DOWNLOAD_SEQ.NEXTVAL as id from dual" }).rows[0]['ID'];
    var insertSql = "insert into QUALITY_DOWNLOAD (ID, TABLE_CONFIG_ID, TREE_ID, TYPE_, EXPORT_TYPE, START_TIME, CREATOR)" +
        "values (" + downloadId + "," + tableConfigId + "," + treeId + "," + type_ + ", " + exportType + ", '" + nowTime + "', '" + creator + "') ";
    logger.error('insertSql:' + insertSql);
    Things['Thing.DB.Oracle'].RunCommand({ sql: insertSql });

    var postUrl = "";
    if (exportType == 1) {
        postUrl = "/table/generate/quality/photo";
    } else if (exportType == 2) {
        postUrl = "/table/generate/all/photos";
    } else if (exportType == 3) {

    }

    var params = {
        downloadId: downloadId,
        tableConfigId: tableConfigId,
        treeId: treeId,
        creator: creator,
        type_: type_,
        fileName: fileName
    };
    var time = new Date().getTime();
    var url = Things['Thing.UserLogin'].GetNewFileHandleUrl() + postUrl + "?time=" + time;
    for (var key in params) {
        url += "&amp;" + key + "=" + encodeURIComponent(params[key]);
    }
    logger.error('ReqGenerateFile-url:' + url);
    Resources["ContentLoaderFunctions"].PostJSON({
        url: url,
        timeout: 999999999
    });

    res.success = true;
    res.msg = "发起生成文件请求成功";
} catch (error) {
    res.success = false;
    var msg = "ReqGenerateFile-发起生成文件请求失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="reqTestControl">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    reqTestControl
 * @description   请求试验管控系统的接口  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    type    
 * @param    {STRING}    reqUrl    
 * @param    {INTEGER}    tableId    
 * @param    {INTEGER}    endY    
 * @param    {INTEGER}    tableType    
 *
 * @returns    {JSON}
 */
var res = {
	success: false,
	msg: "未发现新的数据"
};
//请求参数
var content = "";
content = '&lt;?xml version="1.0" encoding="utf-8"?&gt;\
		&lt;soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"&gt;\
		  &lt;soap:Body&gt;\
			&lt;GetCheckTableList xmlns="http://tempuri.org/"&gt;\
				&lt;xmlSend&gt;&amp;lt;Request&amp;gt;&amp;lt;TreeId&amp;gt;' + treeId + '&amp;lt;/TreeId&amp;gt;&amp;lt;Type&amp;gt;' + type + '&amp;lt;/Type&amp;gt;&amp;lt;/Request&amp;gt;&lt;/xmlSend&gt;\
			&lt;/GetCheckTableList&gt;\
		  &lt;/soap:Body&gt;\
		&lt;/soap:Envelope&gt;';
//PostXML所需参数
var url = reqUrl + "&amp;TreeID=" + treeId + "&amp;Type=" + type;
logger.error('url:'+url);
var params = {
	// headers: {
	// 	"Content-Type": "text/xml; charset=utf-8"
	// },
	url: url,
	timeout: 6000000
	// content: content
};
var resultXml = Resources["ContentLoaderFunctions"].GetXML(params).toString();

logger.error('resultXml0:'+resultXml);

//添加集成日志
Things["Thing.Integration.LogUtil"].createLog({
	fileName: treeId + "_" + type /* STRING */,
	rsContent: resultXml /* STRING */,
	type: "试验管控系统数据" /* STRING */
});

//解析返回的xml
// var contentXml = resultXml.*:: Body.*:: GetCheckTableListResponse;

// resultXml = String(contentXml.*:: GetCheckTableListResult);

resultXml = resultXml.substring(resultXml.indexOf("&lt;Response"), resultXml.indexOf("&lt;/Response&gt;") + 11);
logger.error('resultXml1:'+resultXml);
//结果集的xml
var xml = new XML(resultXml);
var index = 0;
//处理结果集
for each(var tag in xml.Record) {
	var taskName = tag.TaskName || '';
	var excelDownLoadURL = tag.ExcelDownLoadURL || '';
	var productId = tag.ProductId || '';

	if (excelDownLoadURL) {
		//文件处理
		var excelFileName = "";
		var excelFilePath = "";
		var excelFileFormat = "";
		var exceljson = Things["Thing.Integration.DataCollect"].downloadFileTest({
			fileName: "",
			filePath: "",
			url: excelDownLoadURL
		});
		if (exceljson.success) {
			excelFileName = exceljson['fileName'];
			excelFilePath = exceljson['filePath'];
			excelFileFormat = exceljson['fileFormat'];
			var relativePath = encodeURIComponent(excelFilePath);
			var creator = encodeURIComponent("自动同步创建");
			var filename = '';
			if (taskName != undefined &amp;&amp; taskName != '' &amp;&amp; taskName != null) {
				filename = encodeURIComponent(excelFileName);
			}

			var p = "?type=" + tableType + "&amp;tableId=" + tableId + "&amp;relativePath=" + relativePath + "&amp;treeId=" + treeId + "&amp;creator=" +
				creator + "&amp;endY=" + endY + "&amp;productId=" + productId + "&amp;filename=" + filename;
			var url = Things['Thing.UserLogin'].GetNewFileHandleUrl() + "/table/import/mes" + p;
			logger.error("url" + index++ + ":" + url);
			var params = {
				url: url /* STRING */,
			};
			// result: JSON
			res = Resources["ContentLoaderFunctions"].PostJSON(params);
		}
	}
}

result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="ReqTestTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    ReqTestTable
 * @description   请求单个试验管控系统的表格 wanghq 2022年8月31日13:16:55
 * @implementation    {Script}
 *
 * @param    {STRING}    reqUrl    
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    interfaceName    
 * @param    {NUMBER}    tableId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var getUrl = reqUrl + "&amp;TreeID=" + treeId + "&amp;Type=" + interfaceName;
    if (reqUrl.indexOf("?") == -1) {
        getUrl = reqUrl + "?TreeID=" + treeId + "&amp;Type=" + interfaceName;
    }

    var params = {
        url: getUrl,
        timeout: 6000000
    };
    var resultXml = Resources["ContentLoaderFunctions"].GetXML(params).toString();

    //添加集成日志
    Things["Thing.Integration.LogUtil"].createLog({
        fileName: treeId + "_" + interfaceName /* STRING */,
        rsContent: resultXml /* STRING */,
        type: "试验管控系统数据" /* STRING */
    });

    resultXml = resultXml.substring(resultXml.indexOf("&lt;Response"), resultXml.indexOf("&lt;/Response&gt;") + 11);
    //结果集的xml
    var xml = new XML(resultXml);
    //处理结果集
    for each(var tag in xml.Record) {
        var taskName = tag.TaskName || '';
        var excelDownLoadURL = tag.ExcelDownLoadURL || '';
        var photos = tag.Photos.toString();
        if (photos == undefined || photos == "" || photos == null || photos == " ") {
            photos = "[]";
        }
        logger.error('typeof photos:' + typeof photos);
        logger.error('photos:' + photos);
        if (excelDownLoadURL) {
            //文件处理
            var excelFileName = "";
            var excelFilePath = "";
            var excelFileFormat = "";
            var exceljson = Things["Thing.Integration.DataCollect"].downloadFileTest({
                fileName: "",
                filePath: "",
                url: excelDownLoadURL
            });
            if (exceljson.success) {
                excelFileName = exceljson['fileName'];
                excelFilePath = exceljson['filePath'];
                excelFileFormat = exceljson['fileFormat'];
                var excelPath = encodeURIComponent(excelFilePath);
                var creator = encodeURIComponent("自动同步创建");
                var filename = '';
                if (taskName != undefined &amp;&amp; taskName != '' &amp;&amp; taskName != null) {
                    filename = encodeURIComponent(excelFileName);
                }
                var tableHeader = '0';
                var p = "?excelPath=" + excelPath + "&amp;photos=" + encodeURIComponent(photos);
                var url = Things['Thing.UserLogin'].GetNewFileHandleUrl() + "/report/get/excel" + p;
                var params = {
                    url: url
                };
                var excelRs = Resources["ContentLoaderFunctions"].PostJSON(params);
                logger.error("excelRs:" + JSON.stringify(excelRs));
                if (excelRs.success) {
                    var saveData = excelRs.data;
                    //添加表数据到TEST_TABLE表中
                    var htmlData = Things["Thing.Util.HandsonTable"].TableData2Html({ str: saveData });
                    saveData = me.StrToClobSql({ str: saveData });
                    htmlData = me.StrToClobSql({ str: htmlData });
                    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
                    //查询是否已经存在表格了
                    var testTable = Things['Thing.DB.Oracle'].RunQuery({ sql: "select id from test_table where table_config_id=" + tableId + " and tree_id=" + treeId });
                    var reportSourceId = 0;//确认表数据来源ID
                    var commandSql = "";
                    if (testTable.length &gt; 0) {
                        //更新数据
                        // reportSourceId = testTable[0].ID;
                        // commandSql = "update TEST_TABLE set FILEPATH='" + excelFilePath + "', FILENAME='" + excelFileName + "', \
                        //                 CREATE_TIME='" + nowTime + "', SAVE_DATA=" + saveData + ", HTML_DATA=" + htmlData + ",\
                        //                 TABLE_HEADER='" + tableHeader + "', URL='" + excelDownLoadURL + "' where id=" + reportSourceId;
                    } else {
                        //插入数据
                        commandSql = "insert into TEST_TABLE (ID, TABLE_CONFIG_ID, TREE_ID, FILEPATH, FILENAME, CREATE_TIME, SAVE_DATA, HTML_DATA,\
                            TABLE_HEADER, URL)\
                            values (TEST_TABLE_SEQ.nextval,"+ tableId + "," + treeId + ",'" + excelFilePath + "','" + excelFileName + "','" + nowTime + "'\
                            ," + saveData + "," + htmlData + ",'" + tableHeader + "','" + excelDownLoadURL + "')";

                        logger.error('commandSql:' + commandSql);
                        Things['Thing.DB.Oracle'].RunCommand({ sql: commandSql });

                        //更新AIT质量确认表的相关数据
                        var updateReprotSql = "update quality_report set SAVE_DATA=" + saveData + ",HTML_DATA=" + htmlData + ",TABLE_STATUS='sign',\
                                                    TABLE_HEADER='" + tableHeader + "',SAVE_TIME='" + nowTime + "',\SAVE_USER='试验管控系统同步',\
                                                    COPY_ID="+ reportSourceId + " where TREE_ID=" + treeId + " and DATA_SOURCE='test' and TABLE_STATUS='edit' and DATA_TYPE=" + tableId;
                        logger.error('updateReprotSql:' + updateReprotSql);
                        Things['Thing.DB.Oracle'].RunCommand({ sql: updateReprotSql });
                    }

                }
            }
        }
    }
    res.success = true;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    var msg = "请求单个试验管控系统的表格失败，原因：" + error;
    logger.error('msg:' + msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="StrToClobSql">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    StrToClobSql
 * @description   字符串转成可插入的clob类型的sql  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    str    
 *
 * @returns    {STRING}
 */
var len = str.length;
var arr = [];
var maxLen = 10;
for (var i = 0; i &lt; len; i += maxLen) {
    var next = i + maxLen;
    if (next &gt; len) {
        next = len;
    }
    arr.push("to_clob('" + str.substring(i, next) + "')");
}
result = arr.join("||");</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="SyncMesData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    SyncMesData
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {NOTHING}
 */
var reqUrl = Things["Thing.Fn.SystemDic"].getKeyByNames({
    name: "MES项目清单接口路径",
    pname: "系统配置"
});

var reqUrl1 = Things["Thing.Fn.SystemDic"].getKeyByNames({
    name: "试验管控系统接口路径",
    pname: "系统配置"
});

try {
    var mesTable = me.QueryMesTable();
    var allCount = mesTable.rows.length;
    for (var x = 0; x &lt; allCount; x++) {
        var row = mesTable.rows[x];
        var tableId = row.ID;
        var endY = row.SECOND_DATA_ROWNUM;
        var type = row.MES_INTERFACE;
        var tableType = row.TYPE;
        var treeName = row.TREE_NAME;
        var treeTable = me.QueryTreeNodeByName({
            id: tableId
        });
        for (var m = 0; m &lt; treeTable.rows.length; m++) {
            var treeNode = treeTable.rows[m];
            var treeId = treeNode.TREEID;
            if (type.indexOf("S:") &gt; -1) {
                me.reqTestControl({
                    treeId: treeId /* STRING */,
                    type: type.substr(2) /* STRING */,
                    reqUrl: reqUrl1 /* STRING */,
                    tableId: tableId /* STRING */,
                    endY: endY /* STRING */,
                    tableType: tableType
                });
            } else {
                me.testMes({
                    treeId: treeId /* STRING */,
                    type: type /* STRING */,
                    reqUrl: reqUrl /* STRING */,
                    tableId: tableId /* STRING */,
                    endY: endY /* STRING */,
                    tableType: tableType
                });
            }
        }
    }
} catch (e) {
    logger.error("Thing.Fn.TableConfig-Subscriptions-error:" + e);
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="SyncTestConfirmTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    SyncTestConfirmTable
 * @description   同步试验管控系统的确认表 wanghq 2022年8月31日10:21:26
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {

    var reqUrl = Things["Thing.Fn.SystemDic"].getKeyByNames({
        name: "试验管控系统接口路径",
        pname: "系统配置"
    });

    //查询所有数据来源为试验管控系统的表模板 并且接口不能为空 且以 "S:" 开头的
    var tables = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from table_config where type='6' and MES_INTERFACE like 'S:%'" });
    for (var i = 0; i &lt; tables.length; i++) {
        var table = tables[i];
        var tableId = table.ID;
        //接口名称
        var interfaceName = table.MES_INTERFACE.substr(2);
        //查询出所有需要同步数据的过程节点
        var trees = me.QueryTreeNodeByName({
            id: tableId
        });
        for (var m = 0; m &lt; trees.length; m++) {
            var tree = trees.rows[m];
            var treeId = tree.TREEID;
            if (treeId !== 36) {
                continue;
            }
            me.ReqTestTable({
                reqUrl: reqUrl,
                treeId: treeId,
                interfaceName: interfaceName,
                tableId: tableId
            });
        }
    }

    res.success = true;
    res.data = [];
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="test">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    test
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {STRING}
 */
result = dateFormat(new Date(), "yyyy-MM");</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="test2">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    test2
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 *
 * @returns    {XML}
 */
var content = ""; //请求参数
content = '&lt;?xml version="1.0" encoding="utf-8"?&gt;\
		&lt;soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"&gt;\
		  &lt;soap:Body&gt;\
			&lt;GetCheckTableList xmlns="http://tempuri.org/"&gt;\
				&lt;xmlSend&gt;&amp;lt;Request&amp;gt;&amp;lt;TreeId&amp;gt;' + treeId + '&amp;lt;/TreeId&amp;gt;&amp;lt;Type&amp;gt;StandAlong&amp;lt;/Type&amp;gt;&amp;lt;/Request&amp;gt;&lt;/xmlSend&gt;\
			&lt;/GetCheckTableList&gt;\
		  &lt;/soap:Body&gt;\
		&lt;/soap:Envelope&gt;';
//PostXML所需参数
var params = {
	headers: {
		"Content-Type": "text/xml; charset=utf-8"
	},
	url: 'http://***********:81/WebServices/WebServiceForDataPackage.asmx',
	timeout: 6000000,
	content: content
};
var result = Resources["ContentLoaderFunctions"].PostXML(params);</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="testclob">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    testclob
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    clob    
 *
 * @returns    {NOTHING}
 */
var len = clob.length;
var arr = [];
var maxLen = 30000;
for(var i = 0;i&lt;len;i+=maxLen){
    var next = i+maxLen;
    if(next&gt;len){
        next = len;
    }
	arr.push("'"+clob.substring(i,next)+"'");
}
clob = arr.join("||");

var a = "DECLARE"+
    " V_LANG CLOB := "+clob+";"+
" BEGIN"+
    " INSERT INTO SECOND_TABLE1(ID,V0) VALUES (SECOND_TABLE_SEQ1.nextval, V_LANG);"+
    " COMMIT;"+
" END;";
logger.error(a);
Things["Thing.DB.Oracle"].RunCommand({
		sql: a
	});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="testclob2">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    testclob2
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    clob    
 *
 * @returns    {NOTHING}
 */
var len = clob.length;
var arr = [];
var maxLen = 1330;
for (var i = 0; i &lt; len; i += maxLen) {
    var next = i + maxLen;
    if (next &gt; len) {
        next = len;
    }
    arr.push("to_clob('" + clob.substring(i, next) + "')");
}
var clobSql = arr.join("||");

var a = "INSERT INTO SECOND_TABLE1(ID,V0) VALUES (SECOND_TABLE_SEQ1.nextval, " + clobSql + ")";
logger.error(a);
Things["Thing.DB.Oracle"].RunCommand({
    sql: a
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="testMes">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    testMes
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    type    
 * @param    {STRING}    reqUrl    
 * @param    {INTEGER}    tableId    
 * @param    {INTEGER}    endY    
 * @param    {INTEGER}    tableType    
 *
 * @returns    {JSON}
 */
var res = {
	success: false,
	msg: "未发现新的数据"
};
//请求参数
var content = "";
content = '&lt;?xml version="1.0" encoding="utf-8"?&gt;\
		&lt;soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"&gt;\
		  &lt;soap:Body&gt;\
			&lt;GetCheckTableList xmlns="http://tempuri.org/"&gt;\
				&lt;xmlSend&gt;&amp;lt;Request&amp;gt;&amp;lt;TreeId&amp;gt;' + treeId + '&amp;lt;/TreeId&amp;gt;&amp;lt;Type&amp;gt;' + type + '&amp;lt;/Type&amp;gt;&amp;lt;/Request&amp;gt;&lt;/xmlSend&gt;\
			&lt;/GetCheckTableList&gt;\
		  &lt;/soap:Body&gt;\
		&lt;/soap:Envelope&gt;';
//PostXML所需参数
var params = {
	headers: {
		"Content-Type": "text/xml; charset=utf-8"
	},
	url: reqUrl,
	timeout: 6000000,
	content: content
};
var resultXml = Resources["ContentLoaderFunctions"].PostXML(params);
//添加集成日志
Things["Thing.Integration.LogUtil"].createLog({
	fileName: treeId + "_" + type /* STRING */,
	rsContent: resultXml /* STRING */,
	type: "质量数据" /* STRING */
});

//解析返回的xml
var contentXml = resultXml.*:: Body.*:: GetCheckTableListResponse;

resultXml = String(contentXml.*:: GetCheckTableListResult);
resultXml = resultXml.substring(resultXml.indexOf("&lt;Response"), resultXml.indexOf("&lt;/Response&gt;") + 11);
//结果集的xml
var xml = new XML(resultXml);
var index = 0;
//处理结果集
for each(var tag in xml.Record) {
	var checkTableName = tag.CheckTableName || '';
	var excelDownLoadURL = tag.ExcelDownLoadURL || '';
	var productId = tag.ProductId || '';

	if (excelDownLoadURL) {
		//文件处理
		var excelFileName = "";
		var excelFilePath = "";
		var excelFileFormat = "";
		var exceljson = Things["Thing.Integration.DataCollect"].downloadFileTest({
			fileName: "",
			filePath: "",
			url: excelDownLoadURL
		});
		if (exceljson.success) {
			excelFileName = exceljson['fileName'];
			excelFilePath = exceljson['filePath'];
			excelFileFormat = exceljson['fileFormat'];
			var relativePath = encodeURIComponent(excelFilePath);
			var creator = encodeURIComponent("自动同步创建");
			var filename = '';
			if (checkTableName != undefined &amp;&amp; checkTableName != '' &amp;&amp; checkTableName != null) {
				filename = encodeURIComponent(excelFileName);
			}
			var p = "?type=" + tableType + "&amp;tableId=" + tableId + "&amp;relativePath=" + relativePath + "&amp;treeId=" + treeId + "&amp;creator=" +
				creator + "&amp;endY=" + endY + "&amp;productId=" + productId + "&amp;filename=" + filename;
			//var url = Things['Thing.UserLogin'].GetNewFileHandleUrl() + "/table/import/mes" + p;
            var url = "http://127.0.0.1:8011/FileHandle/table/import/mes" + p;
			logger.error("url" + index++ + ":" + url);
			var params = {
				url: url /* STRING */,
			};
			// result: JSON
			res = Resources["ContentLoaderFunctions"].PostJSON(params);
		}
	}
}

result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="TestXml">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    TestXml
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {XML}
 */
result = '&lt;?xml version="1.0" encoding="utf-8"?&gt;\
&lt;Response&gt;\
    &lt;Record&gt;\
        &lt;Id&gt;1015&lt;/Id&gt;\
        &lt;FormName&gt;ABBCCD&lt;/FormName&gt;\
        &lt;TaskName&gt;风云四号01星真空热试验&lt;/TaskName&gt;\
        &lt;ExcelDownLoadURL&gt;http://127.0.0.1:8011/File//xml/test1.xls&lt;/ExcelDownLoadURL&gt;\
		&lt;Photos&gt;[{photoDownLoadURL: "http://127.0.0.1:8011/File//2022-08//4d8cf1f3-b52a-4e85-99da-38ebd4a135af",type:"sign",date:"2022-08-31",row:"1",col:"1"},{photoDownLoadURL: "http://127.0.0.1:8011/File//2022-08//4d8cf1f3-b52a-4e85-99da-38ebd4a135af",type:"photo",date:"2022-08-31",row:"2",col:"2"},{photoDownLoadURL: "http://127.0.0.1:8011/File//2022-08//4d8cf1f3-b52a-4e85-99da-38ebd4a135af",type:"sign",date:"2022-08-31",row:"3",col:"3"}]&lt;/Photos&gt;\
        &lt;SecurityLevel&gt;&lt;/SecurityLevel&gt;\
        &lt;ProductId&gt;156&lt;/ProductId&gt;\
    &lt;/Record&gt;\
&lt;/Response&gt;';</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="TestXml1">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    TestXml1
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {XML}
 */
result = '&lt;?xml version="1.0" encoding="utf-8"?&gt;\
&lt;Response&gt;\
    &lt;Record&gt;\
        &lt;Id&gt;1015&lt;/Id&gt;\
        &lt;FormName&gt;ABBCCD&lt;/FormName&gt;\
        &lt;TaskName&gt;风云四号01星真空热试验&lt;/TaskName&gt;\
        &lt;ExcelDownLoadURL&gt;http://127.0.0.1:8011/File//xml/test1.xls&lt;/ExcelDownLoadURL&gt;\
		&lt;Photos&gt;[{photoDownLoadURL: "http://127.0.0.1:8011/File//2022-08//4d8cf1f3-b52a-4e85-99da-38ebd4a135af",type:"sign",date:"2022-08-31",row:"1",col:"1"},{photoDownLoadURL: "http://127.0.0.1:8011/File//2022-08//4d8cf1f3-b52a-4e85-99da-38ebd4a135af",type:"photo",date:"2022-08-31",row:"2",col:"2"},{photoDownLoadURL: "http://127.0.0.1:8011/File//2022-08//4d8cf1f3-b52a-4e85-99da-38ebd4a135af",type:"sign",date:"2022-08-31",row:"3",col:"3"}]&lt;/Photos&gt;\
        &lt;SecurityLevel&gt;&lt;/SecurityLevel&gt;\
        &lt;ProductId&gt;156&lt;/ProductId&gt;\
    &lt;/Record&gt;\
&lt;/Response&gt;';</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="transbase10">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    transbase10
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    str    
 *
 * @returns    {NUMBER}
 */
var n = 0;
var s = str.match(/./g); //求出字符数组

for(var i = str.length - 1, j = 1; i &gt;= 0; i--, j *= 26){
	var c = s[i].toUpperCase();
   	if(c&lt;'A'||c&gt;'Z'){
    	n = 0;
    }
    n += (c.charCodeAt(0) - 64) * j;
}
result = n;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UpdateHistoryData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UpdateHistoryData
 * @description   更新历史数据  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {STRING}
 */
//通过之前的字段名获取新的字段名
function getColNameByOldCol(params, oldColName) {
    var colName = "";
    if (oldColName.indexOf("val") &gt; -1) {
        var colIndex = oldColName.split("val")[1];
        for (var i = 0; i &lt; params.rows.length; i++) {
            var param = params.rows[i];
            var newColName = param["COL_NAME"];
            var secondArea = param["SECOND_AREA"];
            var isCapital = me.isCapital({ str: secondArea });
            if (isCapital) {
                var index = me.transbase10({
                    str: secondArea
                });
                if (colIndex == index) {
                    colName = newColName;
                    break;
                }
            }
        }
    }
    return colName;
}
//将旧数据转换为新数据的格式
function oldDataToNewData(params, oldData) {
    var newData = {};
    for (var key in oldData) {
        var colName = getColNameByOldCol(params, key);
        if (colName != "") {
            newData[colName] = oldData[key];
        }
    }
    return newData;
}
var startTime = new Date().getTime();
var tableSql = "select * from table_config";
//获取所有的配置表
var tables = Things["Thing.DB.Oracle"].RunQuery({
    sql: tableSql
});
for (var x = 0; x &lt; tables.rows.length; x++) {
    var table = tables.rows[x];
    var tableId = table["ID"];

    //查询表的所有属性
    var paramsSql = "select * from param_config where TABLE_ID=" + tableId + " order by ID";
    var params = Things["Thing.DB.Oracle"].RunQuery({
        sql: paramsSql
    });

    //查询表的所有数据
    var tableDataSql = "select * from table_data where TABLE_CONFIG_ID=" + tableId;
    var tableDatas = Things["Thing.DB.Oracle"].RunQuery({
        sql: tableDataSql
    });

    for (var m = 0; m &lt; tableDatas.rows.length; m++) {
        var tableData = tableDatas.rows[m];
        var treeId = tableData["TREE_ID"];
        var filepath = tableData["FILEPATH"];
        var filename = tableData["FILENAME"];
        var creator = tableData["CREATOR"];
        var createTime = tableData["CREATE_TIME"];
        var dataStr = tableData["DATA"];
        var data = JSON.parse(dataStr);
        var isArray = Array.isArray(data);
        if (!isArray) {
            var newData = oldDataToNewData(params, data);
            logger.error("newData:" + JSON.stringify(newData));
            me.AddTableData({
                TABLE_CONFIG_ID: tableId,
                TREE_ID: treeId,
                FILEPATH: filepath,
                creator: creator,
                FILENAME: filename,
                createTime: createTime,
                data: newData
            });
        } else {
            for (var i = 0; i &lt; data.length; i++) {
                var d = data[i];
                var newData = oldDataToNewData(params, d);
                me.AddTableData({
                    TABLE_CONFIG_ID: tableId,
                    TREE_ID: treeId,
                    FILEPATH: filepath,
                    creator: creator,
                    FILENAME: filename,
                    createTime: createTime,
                    data: newData
                });
            }
        }
    }
}
var endTime = new Date().getTime();

var time = endTime - startTime;
var timeStr = Things["Thing.Integration.DataCollect"].msecToTime({
    msec: time
});
result = "耗时" + timeStr;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UpdateHistoryTableConfig">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UpdateHistoryTableConfig
 * @description   更新历史的表格配置表  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {STRING}
 */
var startTime = new Date().getTime();
var tableSql = "select * from table_config";

//清除表信息

var tables = Things["Thing.DB.Oracle"].RunQuery({
    sql: tableSql /* STRING */
});

for (var x = 0; x &lt; tables.rows.length; x++) {
    var table = tables.rows[x];
    var tableName = table["TABLE_NAME"];
    //如果不存在表名称的话 需要创建表
    if (tableName == undefined || tableName == "") {
        var tableId = table["ID"];
        //表名称序号
        var tableNameSeq = Things["Thing.DB.Oracle"].RunQuery({
            sql: "select TABLE_NAME_SEQ.nextval NAME_SEQ from dual"
        }).rows[0].NAME_SEQ;
        //表名称
        tableName = "SECOND_TABLE" + tableNameSeq;
        //序列名称
        var seqName = "SECOND_TABLE_SEQ" + tableNameSeq;
        var createTableSql = "create table " + tableName + "(ID NUMBER not null" + " primary key," + "TABLE_CONFIG_ID NUMBER," + "TREE_ID NUMBER," + "PRODUCT_ID NUMBER," + "STATUS VARCHAR2(255)," + "FILEPATH VARCHAR2(255)," + "FILENAME VARCHAR2(255)," + "CREATOR VARCHAR2(255)," + "CREATE_TIME VARCHAR2(255))";
        //创建表
        Things["Thing.DB.Oracle"].RunCommand({
            sql: createTableSql
        });
        //创建序列
        var createSeqSql = "create sequence " + seqName + " maxvalue 999999999";
        Things["Thing.DB.Oracle"].RunCommand({
            sql: createSeqSql
        });
        //更新配置表的字段
        var updateTableSql = "update table_config set TABLE_NAME='" + tableName + "',SEQ_NAME='" + seqName + "' where ID=" + tableId;
        Things["Thing.DB.Oracle"].RunCommand({
            sql: updateTableSql /* STRING */
        });
        //接下来处理字段
        var paramsSql = "select * from param_config where TABLE_ID=" + tableId + " order by ID";
        var params = Things["Thing.DB.Oracle"].RunQuery({
            sql: paramsSql /* STRING */
        });

        for (var y = 0; y &lt; params.rows.length; y++) {
            var param = params.rows[y];
            var colName = param["COL_NAME"];
            if (colName == undefined || colName == "") {
                var paramId = param["ID"];
                var paramFormat = param["FORMAT"];
                //字段编号
                var paramIndex = me.GetNewParamSeq({
                    tableName: tableName
                });
                //字段名称
                var columnName = "V" + paramIndex;
                var colunmType = "VARCHAR2(2000)";
                //影像记录或者跟踪卡链接
                if (paramFormat == "3" || paramFormat == "4") {
                    colunmType = "CLOB";
                }
                //添加列
                var alterSql = "alter table " + tableName + " add " + columnName + " " + colunmType;
                Things["Thing.DB.Oracle"].RunCommand({
                    sql: alterSql /* STRING */
                });

                //更新属性表的字段
                var updateParamSql = "update param_config set TABLE_NAME='" + tableName + "',COL_NAME='" + columnName + "' where ID=" + paramId;
                Things["Thing.DB.Oracle"].RunCommand({
                    sql: updateParamSql /* STRING */
                });
            }
        }
    }
}
var endTime = new Date().getTime();

var time = endTime - startTime;
var timeStr = Things["Thing.Integration.DataCollect"].msecToTime({
    msec: time
});
result = "耗时" + timeStr;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UpdateHistoyrTableAndData">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UpdateHistoyrTableAndData
 * @description   更新表结构以及同步历史数据  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 *
 * @returns    {STRING}
 */
var startTime = new Date().getTime();
me.ClearTable();
me.UpdateHistoryTableConfig();
me.UpdateHistoryData();

var endTime = new Date().getTime();

var time = endTime - startTime;
var timeStr = Things["Thing.Integration.DataCollect"].msecToTime({
    msec: time
});
result = "耗时" + timeStr;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UpdateParam">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UpdateParam
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    PARAM_NAME    
 * @param    {STRING}    MES_NAME    
 * @param    {STRING}    THREE_AREA    
 * @param    {STRING}    modifier    
 * @param    {STRING}    ID    
 * @param    {STRING}    SECOND_AREA    
 * @param    {STRING}    FORMAT    
 * @param    {STRING}    WIDTH    
 * @param    {INTEGER}    IS_REMOVE_REPEAT    
 * @param    {INTEGER}    IS_SORT    
 * @param    {INTEGER}    TABLE_ID    
 * @param    {INTEGER}    VALUE_TYPE    
 * @param    {INTEGER}    RELATION_PARAM    
 * @param    {INTEGER}    IS_BASE    
 * @param    {STRING}    INTERFACE_NAME        {"aspect.defaultValue":" "}
 * @param    {INTEGER}    IS_INDEX        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    IS_QUERY        {"aspect.defaultValue":"0"}
 * @param    {INTEGER}    IS_SHOW_IN_360        {"aspect.defaultValue":"0"}
 *
 * @returns    {NUMBER}
 */
var param = Things["Thing.DB.Oracle"].RunQuery({
    sql: "select * from param_config where id=" + ID
});
if (VALUE_TYPE == 0) {
    var oldRelationParam = param.rows[0]["RELATION_PARAM"];
    Things["Thing.DB.Oracle"].RunCommand({
        sql: "update PARAM_CONFIG set VALUE_TYPE=0 , RELATION_PARAM=0 where ID=" + oldRelationParam
    });
    RELATION_PARAM = 0;
}
var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
if (!IS_REMOVE_REPEAT) {
    IS_REMOVE_REPEAT = 0;
}
if (!IS_SORT) {
    IS_SORT = 0;
}
if (!IS_BASE) {
    IS_BASE = 0;
}
if (IS_SORT != 0) {
    Things["Thing.DB.Oracle"].RunCommand({
        sql: "update PARAM_CONFIG set IS_SORT=0 where ID!='" + ID + "' and TABLE_ID='" + TABLE_ID + "'"
    });
}
// if (IS_REMOVE_REPEAT != 0 &amp;&amp; IS_SORT != 0) {
//     Things["Thing.DB.Oracle"].RunCommand({
//         sql: "update PARAM_CONFIG set IS_REMOVE_REPEAT=0 , IS_SORT=0 where ID!='" + ID + "' and TABLE_ID='" + TABLE_ID + "'"
//     });
// } else if (IS_SORT != 0 &amp;&amp; IS_REMOVE_REPEAT == 0) {
//     Things["Thing.DB.Oracle"].RunCommand({
//         sql: "update PARAM_CONFIG set IS_SORT=0 where ID!='" + ID + "' and TABLE_ID='" + TABLE_ID + "'"
//     });
// } else if (IS_SORT == 0 &amp;&amp; IS_REMOVE_REPEAT != 0) {
//     Things["Thing.DB.Oracle"].RunCommand({
//         sql: "update PARAM_CONFIG set IS_REMOVE_REPEAT=0 where ID!='" + ID + "' and TABLE_ID='" + TABLE_ID + "'"
//     });
// }

//需要将列定义为CLOB类型
if (FORMAT == "3" || FORMAT == "4") {
    var oldFormat = param.rows[0].FORMAT;
    if (oldFormat != "3" &amp;&amp; oldFormat != "4") {
        var colName = param.rows[0].COL_NAME;
        var tableName = param.rows[0].TABLE_NAME;
        var newColName = "V" + me.GetNewParamSeq({ tableName: tableName });
        var alterSql = "alter table " + tableName + " add " + newColName + " CLOB";
        Things["Thing.DB.Oracle"].RunCommand({
            sql: alterSql
        });
        //将原来列的数据转移到新的列上
        var updateSql = "update " + tableName + " t1 set t1." + newColName + "=(select t2." + colName + " from " + tableName + " t2 where t1.ID=t2.ID)";
        Things["Thing.DB.Oracle"].RunCommand({
            sql: updateSql
        });
    }
}
//现将接口属性置空再设置 避免一个接口属性有两个值
if (INTERFACE_NAME != "" &amp;&amp; INTERFACE_NAME != " ") {
    Things['Thing.DB.Oracle'].RunCommand({ sql: "update PARAM_CONFIG set INTERFACE_NAME='' where TABLE_ID=" + TABLE_ID + "  and INTERFACE_NAME='" + INTERFACE_NAME + "'" });
}
var sql = "update PARAM_CONFIG set PARAM_NAME='" + PARAM_NAME + "',MES_NAME='" + MES_NAME + "',THREE_AREA='" + THREE_AREA + "',SECOND_AREA='" + SECOND_AREA + "',WIDTH='" + WIDTH + "',FORMAT='" + FORMAT + "',MODIFIER='" + modifier + "',IS_REMOVE_REPEAT='" + IS_REMOVE_REPEAT + "',RELATION_PARAM='" + RELATION_PARAM + "',VALUE_TYPE='" + VALUE_TYPE + "',IS_SORT='" + IS_SORT + "',IS_BASE='" + IS_BASE + "',IS_INDEX='" + IS_INDEX + "',IS_QUERY='" + IS_QUERY + "',IS_SHOW_IN_360='" + IS_SHOW_IN_360 + "',INTERFACE_NAME='" + INTERFACE_NAME + "',UPDATE_TIME='" + now + "' where ID='" + ID + "'";
result = Things["Thing.DB.Oracle"].RunCommand({
    sql: sql /* STRING */
});

if (RELATION_PARAM != 0) {
    var valueType = VALUE_TYPE == 1 ? 2 : 1;
    //更新相关属性的值
    Things["Thing.DB.Oracle"].RunCommand({
        sql: "update PARAM_CONFIG set VALUE_TYPE=" + valueType + ",RELATION_PARAM=" + ID + " where ID=" + RELATION_PARAM
    });
}</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UpdateQualityDataStatus">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UpdateQualityDataStatus
 * @description   行内质量数据状态更新 wanghq 2021年12月3日16:51:01
 * @implementation    {Script}
 *
 * @param    {STRING}    onlyValue    
 * @param    {STRING}    paramId    
 * @param    {INTEGER}    tableConfigId    
 * @param    {STRING}    paramName    
 * @param    {STRING}    creator    
 * @param    {NUMBER}    treeId    
 * @param    {INTEGER}    type    
 * @param    {STRING}    paramValue    
 * @param    {STRING}    status    
 *
 * @returns    {JSON}
 */
var res = {
    success: true
};

try {
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    var selectSql = "select * from QUALITY_SINGLE_STATUS where ONLY_VALUES='" + onlyValue + "' and table_config_id =" + tableConfigId + " and treeid =" + treeId + " and type =" + type + " and PARAM_ID='" + paramId + "'";
    var rs1 = Things["Thing.DB.Oracle"].RunQuery({ sql: selectSql });
    if (rs1.rows.length == 0) {
        //添加操作
        var insertSql = "insert into QUALITY_SINGLE_STATUS (ID, ONLY_VALUES, TABLE_CONFIG_ID, PARAM_ID,PARAM_NAME,PARAM_VALUE, TREEID, TYPE,STATUS, CREATOR, CREATE_TIME)values (QUALITY_STATUS_SEQ.NEXTVAL,'" + onlyValue + "'," + tableConfigId + ",'" + paramId + "','" + paramName + "','" + paramValue + "'," + treeId + "," + type + ",'" + status + "','" + creator + "','" + nowTime + "')";
        logger.error("insertSql:" + insertSql);
        Things["Thing.DB.Oracle"].RunCommand({ sql: insertSql });
    } else {
        //更新操作
        var id = rs1.rows[0]["ID"];
        var updateSql = "update QUALITY_SINGLE_STATUS set creator='" + creator + "',PARAM_NAME='" + paramName + "',PARAM_ID='" + paramId + "',PARAM_VALUE='" + paramValue + "',CREATE_TIME='" + nowTime + "',STATUS='" + status + "' where id=" + id;
        Things["Thing.DB.Oracle"].RunCommand({ sql: updateSql });
    }
    res.msg = "更新成功";
} catch (error) {
    res.success = false;
    res.msg = "更新失败，原因：" + error;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UpdateTable">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UpdateTable
 * @description   wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {STRING}    TREE_NAME    
 * @param    {STRING}    TYPE    
 * @param    {STRING}    MES_INTERFACE    
 * @param    {STRING}    modifier    
 * @param    {STRING}    ID    
 * @param    {STRING}    SECOND_DATA_ROWNUM    
 * @param    {STRING}    THREE_DATA_ROWNUM    
 * @param    {INTEGER}    PLAN_START_COLINDEX    
 * @param    {INTEGER}    PLAN_END_COLINDEX    
 * @param    {INTEGER}    PHOTO_START_COLINDEX    
 * @param    {INTEGER}    PHOTO_END_COLINDEX    
 * @param    {INTEGER}    IS_QUERY        {"aspect.defaultValue":"0"}
 *
 * @returns    {NUMBER}
 */
var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
                                        
var sql = "update TABLE_CONFIG set TREE_NAME='" + TREE_NAME + "',TYPE='" + TYPE + "',MES_INTERFACE='" + MES_INTERFACE + "',IS_QUERY='" + IS_QUERY + "',PHOTO_START_COLINDEX='" + PHOTO_START_COLINDEX + "',PHOTO_END_COLINDEX='" + PHOTO_END_COLINDEX + "',PLAN_START_COLINDEX='" + PLAN_START_COLINDEX + "',PLAN_END_COLINDEX='" + PLAN_END_COLINDEX + "',SECOND_DATA_ROWNUM='" + SECOND_DATA_ROWNUM + "',THREE_DATA_ROWNUM='" + THREE_DATA_ROWNUM + "',MODIFIER='" + modifier + "',UPDATE_TIME='" + now + "' where ID='" + ID + "'";
result = Things["Thing.DB.Oracle"].RunCommand({
    sql: sql /* STRING */
});</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UpdateTableTplFile">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UpdateTableTplFile
 * @description   更新二级表、三级表模板位置  wanghq 2025年5月22日9:55:53
 * @implementation    {Script}
 *
 * @param    {INTEGER}    type    
 * @param    {STRING}    filepath    
 * @param    {STRING}    modifier    
 * @param    {INTEGER}    id    
 *
 * @returns    {JSON}
 */
var rs = {
    success: true
};
try {
    var now = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    var field = "";
    if (type == 2) {
        field = "SECOND_FILEPATH";
    } else if (type == 3) {
        field = "THREE_FILEPATH";
    } else if (type == 4) {
        field = "PLAN_FILEPATH";
    } else if (type == 5) {
        field = "PHOTO_FILEPATH";
    } else if (type == 7) {
        field = "CERTIFICATE_FILEPATH";
    }

    var sql = "update TABLE_CONFIG set " + field + "='" + filepath + "',MODIFIER='" + modifier + "',UPDATE_TIME='" + now + "' where ID=" + id;
    Things["Thing.DB.Oracle"].RunCommand({
        sql: sql
    });
    rs.msg = "上传成功";
} catch (error) {
    rs.success = false;
    rs.msg = "上传失败，原因：" + error;
}
result = rs;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
          <ServiceImplementation description="" handlerName="Script" name="UploadTableImage">
            <ConfigurationTables>
              <ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0">
                <DataShape>
                  <FieldDefinitions>
                    <FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/>
                  </FieldDefinitions>
                </DataShape>
                <Rows>
                  <Row>
                    <code>/**
 * @definition    UploadTableImage
 * @description   上传二级表中的图片 datetime 2023年12月19日16:27:47
 * @implementation    {Script}
 *
 * @param    {STRING}    tableName    
 * @param    {STRING}    colName    
 * @param    {NUMBER}    dataId    
 * @param    {STRING}    filePath    
 * @param    {STRING}    fileFormat    
 * @param    {STRING}    fileName    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    var fileObj = {
        images: [{
            filePath: filePath,
            fileFormat: fileFormat,
            fileName: fileName,
            creator: username,
            createTime: nowTime,
        }]
    };
    var fileObjStr = JSON.stringify(fileObj);
    var updateSql = "UPDATE " + tableName + " SET " + colName + " = '" + fileObjStr + "' WHERE id = " + dataId + "";
    Things['Thing.DB.Oracle'].RunCommand({ sql: updateSql });
    res.success = true;
    res.data = [];
    res.msg = "上传二级表中的附件成功";
} catch (error) {
    res.success = false;
    var msg = "UploadTableImage-上传二级表中的附件失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code>
                  </Row>
                </Rows>
              </ConfigurationTable>
            </ConfigurationTables>
          </ServiceImplementation>
        </ServiceImplementations>
        <Subscriptions/>
      </ThingShape>
      <PropertyBindings/>
      <RemotePropertyBindings/>
      <RemoteServiceBindings/>
      <RemoteEventBindings/>
      <AlertConfigurations/>
      <ImplementedShapes/>
      <ThingProperties/>
    </Thing>
  </Things>
</Entities>