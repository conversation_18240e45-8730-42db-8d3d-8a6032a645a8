package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.core.date.DateUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.Pano2VRHotspotInjector;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 全景图热点编辑服务
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
public class PanoramaService extends ApplicationConfig {

    /**
     * 创建新任务
     */
    public JSONObject createTask(String taskName, String modelId, String modelName, String description) {
        try {
            // 生成任务ID
            String taskIdSql = "SELECT SEQ_PANORAMA_TASK.NEXTVAL FROM DUAL";
            JSONArray taskIdResult = Util.postQuerySql(taskIdSql);
            Long taskId = null;
            if (taskIdResult != null && taskIdResult.size() > 0) {
                Object firstRowObj = taskIdResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    taskId = ((JSONObject) firstRowObj).getLong("NEXTVAL");
                }
            }

            // 插入任务记录
            String insertSql = "INSERT INTO PANORAMA_TASK (TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS) " +
                    "VALUES (" + taskId + ", '" + taskName + "', " +
                    (modelId != null ? "'" + modelId + "'" : "NULL") + ", " +
                    (modelName != null ? "'" + modelName + "'" : "NULL") + ", " +
                    (description != null ? "'" + description + "'" : "NULL") + ", 'adm', SYSDATE, SYSDATE, 0)";

            Util.postCommandSql(insertSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务创建成功")
                    .set("data", JSONUtil.createObj().set("taskId", taskId));
        } catch (Exception e) {
            log.error("创建任务失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务列表
     */
    public JSONObject getTaskList() {
        try {
            String sql = "SELECT TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS " +
                    "FROM PANORAMA_TASK ORDER BY CREATE_TIME DESC";
            JSONArray taskList = Util.postQuerySql(sql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", taskList);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    public JSONObject getTaskDetail(Long taskId) {
        try {
            String sql = "SELECT * FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray result = Util.postQuerySql(sql);
            
            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            // 安全获取第一行数据
            Object firstRowObj = result.get(0);
            JSONObject taskData = null;
            if (firstRowObj instanceof JSONObject) {
                taskData = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", taskData);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 检查任务是否已存在热点数据
     */
    public JSONObject checkExistingHotspots(Long taskId) {
        try {
            String countSql = "SELECT COUNT(*) AS TOTAL FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("hasData", total > 0)
                    .set("count", total);
        } catch (Exception e) {
            log.error("检查热点数据失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "检查热点数据失败: " + e.getMessage());
        }
    }

    /**
     * 清理任务的热点数据和文件信息
     */
    public JSONObject clearTaskData(Long taskId) {
        try {
            // 删除热点数据
            String deleteHotspotSql = "DELETE FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteHotspotSql);

            // 清空任务的文件路径信息
            String updateTaskSql = "UPDATE PANORAMA_TASK SET ZIP_FILE_PATH = NULL, EXTRACT_PATH = NULL, UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateTaskSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务数据清理成功");
        } catch (Exception e) {
            log.error("清理任务数据失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "清理任务数据失败: " + e.getMessage());
        }
    }

    /**
     * 上传全景图ZIP包
     */
    public JSONObject uploadPanoramaZip(Long taskId, MultipartFile file) throws IOException {
        try {
            // 创建任务专用目录
            String taskDir = fileUploadPath + File.separator + "panorama" + File.separator + taskId;
            FileUtil.mkdir(taskDir);

            // 保存ZIP文件
            String zipFileName = "panorama_" + System.currentTimeMillis() + ".zip";
            String zipFilePath = taskDir + File.separator + zipFileName;
            file.transferTo(new File(zipFilePath));

            // 解压ZIP文件
            String extractPath = taskDir + File.separator + "extracted";
            ZipUtil.unzip(zipFilePath, extractPath);

            // 查找pano.xml文件
            File panoXmlFile = findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到pano.xml文件");
            }

            // 解析XML并保存热点信息
            parseAndSaveHotspots(taskId, panoXmlFile);

            // 注入热点定位脚本到HTML文件
            try {
                log.info("开始为解压目录注入热点定位脚本: {}", extractPath);
                Pano2VRHotspotInjector.processDirectory(extractPath);
                log.info("热点定位脚本注入完成");
            } catch (Exception e) {
                log.warn("注入热点定位脚本失败，但不影响主流程: {}", e.getMessage());
            }

            // 更新任务信息
            String updateSql = "UPDATE PANORAMA_TASK SET ZIP_FILE_PATH = '" + zipFilePath +
                    "', EXTRACT_PATH = '" + extractPath + "', UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "ZIP文件上传成功")
                    .set("data", JSONUtil.createObj()
                            .set("zipPath", zipFilePath)
                            .set("extractPath", extractPath));
        } catch (Exception e) {
            log.error("上传ZIP文件失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "上传ZIP文件失败: " + e.getMessage());
        }
    }

    /**
     * 查找pano.xml文件
     */
    private File findPanoXmlFile(File dir) {
        if (!dir.exists() || !dir.isDirectory()) {
            return null;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            return null;
        }

        for (File file : files) {
            if (file.isFile() && "pano.xml".equals(file.getName())) {
                return file;
            } else if (file.isDirectory()) {
                File found = findPanoXmlFile(file);
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }

    /**
     * 解析XML并保存热点信息
     */
    private void parseAndSaveHotspots(Long taskId, File panoXmlFile) throws Exception {
        SAXReader reader = new SAXReader();
        Document document = reader.read(panoXmlFile);
        Element root = document.getRootElement();

        // 查找所有panorama节点
        List<Element> panoramas = root.elements("panorama");

        log.info("开始解析XML文件，找到 {} 个panorama节点", panoramas.size());

        for (Element panorama : panoramas) {
            // 获取panorama节点的id属性，用于多节点支持
            String panoramaId = panorama.attributeValue("id");
            if (panoramaId == null || panoramaId.trim().isEmpty()) {
                // 如果没有id属性，使用默认值
                panoramaId = "node1";
                log.warn("Panorama节点没有id属性，使用默认值: {}", panoramaId);
            }

            log.debug("处理panorama节点: {}", panoramaId);

            Element hotspotsElement = panorama.element("hotspots");
            if (hotspotsElement != null) {
                List<Element> hotspots = hotspotsElement.elements("hotspot");
                log.debug("在panorama节点 {} 中找到 {} 个热点", panoramaId, hotspots.size());

                for (Element hotspot : hotspots) {
                    String skinid = hotspot.attributeValue("skinid");
                    // 只处理stand-alone类型的热点
                    if ("stand-alone".equals(skinid)) {
                        // 传递panoramaId参数到保存方法
                        saveHotspotToDatabase(taskId, panoramaId, hotspot);
                    }
                }
            } else {
                log.debug("Panorama节点 {} 没有hotspots元素", panoramaId);
            }
        }

        log.info("XML解析完成，已处理 {} 个panorama节点", panoramas.size());
    }

    /**
     * 保存热点信息到数据库
     * @param taskId 任务ID
     * @param panoramaId 所属全景节点ID
     * @param hotspot 热点XML元素
     */
    private void saveHotspotToDatabase(Long taskId, String panoramaId, Element hotspot) {
        try {
            String hotspotXmlId = hotspot.attributeValue("id");
            String originalTitle = hotspot.attributeValue("title");
            String originalDescription = hotspot.attributeValue("description");
            String pan = hotspot.attributeValue("pan");
            String tilt = hotspot.attributeValue("tilt");
            String skinid = hotspot.attributeValue("skinid");
            String url = hotspot.attributeValue("url");
            String target = hotspot.attributeValue("target");

            // 构建包含PANORAMA_ID字段的INSERT语句
            String insertSql = "INSERT INTO PANORAMA_HOTSPOT " +
                    "(HOTSPOT_ID, TASK_ID, PANORAMA_ID, HOTSPOT_XML_ID, ORIGINAL_TITLE, ORIGINAL_DESCRIPTION, PAN, TILT, SKINID, URL, TARGET, IS_EDITED, CREATE_TIME, UPDATE_TIME) " +
                    "VALUES (SEQ_PANORAMA_HOTSPOT.NEXTVAL, " + taskId + ", " +
                    (panoramaId != null ? "'" + panoramaId + "'" : "NULL") + ", " +
                    (hotspotXmlId != null ? "'" + hotspotXmlId + "'" : "NULL") + ", " +
                    (originalTitle != null ? "'" + originalTitle.replace("'", "''") + "'" : "NULL") + ", " +
                    (originalDescription != null ? "'" + originalDescription.replace("'", "''") + "'" : "NULL") + ", " +
                    (pan != null ? "'" + pan + "'" : "NULL") + ", " +
                    (tilt != null ? "'" + tilt + "'" : "NULL") + ", " +
                    (skinid != null ? "'" + skinid + "'" : "NULL") + ", " +
                    (url != null ? "'" + url + "'" : "NULL") + ", " +
                    (target != null ? "'" + target + "'" : "NULL") + ", " +
                    "0, SYSDATE, SYSDATE)";

            log.debug("保存热点到数据库: taskId={}, panoramaId={}, hotspotXmlId={}, title={}",
                     taskId, panoramaId, hotspotXmlId, originalTitle);

            Util.postCommandSql(insertSql);
        } catch (Exception e) {
            log.error("保存热点信息失败", e);
        }
    }

    /**
     * 上传单机信息Excel文件
     */
    public JSONObject uploadDeviceExcel(Long taskId, MultipartFile file) throws IOException {
        try {
            // 保存Excel文件
            String tempDir = tempPath + File.separator + "excel_" + System.currentTimeMillis();
            FileUtil.mkdir(tempDir);
            String excelPath = tempDir + File.separator + file.getOriginalFilename();
            file.transferTo(new File(excelPath));

            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(new File(excelPath));
            List<List<Object>> rows = reader.read();

            // 跳过表头，从第二行开始读取数据
            for (int i = 1; i < rows.size(); i++) {
                List<Object> row = rows.get(i);
                if (row.size() >= 4) {
                    saveDeviceToDatabase(taskId, row);
                }
            }

            // 清理临时文件
            FileUtil.del(tempDir);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "Excel文件上传成功");
        } catch (Exception e) {
            log.error("上传Excel文件失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "上传Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 保存单机信息到数据库（支持去重更新）
     */
    private void saveDeviceToDatabase(Long taskId, List<Object> row) {
        try {
            Object sequenceNo = row.get(0);
            Object deviceName = row.get(1);
            Object deviceCode = row.get(2);
            Object batchNo = row.get(3);
            // 假设Excel中第5列和第6列是型号ID和型号名称
            Object modelId = row.size() > 4 ? row.get(4) : null;
            Object modelName = row.size() > 5 ? row.get(5) : null;

            // 检查设备是否已存在（基于任务ID、设备名称、设备代号、批次号）
            // 修复NULL值比较问题，使用IS NULL而不是= NULL
            String checkSql = "SELECT COUNT(*) as cnt FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId +
                    " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.toString().replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                    " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.toString().replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                    " AND " + (batchNo != null ? "BATCH_NO = '" + batchNo.toString().replace("'", "''") + "'" : "BATCH_NO IS NULL");

            JSONArray checkResult = Util.postQuerySql(checkSql);
            JSONArray dataArray = checkResult;

            boolean deviceExists = false;
            if (dataArray != null && dataArray.size() > 0) {
                // 修复类型转换错误：从JSONArray中获取JSONObject
                Object firstRowObj = dataArray.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    int count = firstRow.getInt("CNT");
                    deviceExists = count > 0;
                }
            }

            if (deviceExists) {
                // 设备已存在，执行更新操作
                String updateSql = "UPDATE PANORAMA_DEVICE SET " +
                        "SEQUENCE_NO = " + (sequenceNo != null ? sequenceNo.toString() : "NULL") + ", " +
                        "MODEL_ID = " + (modelId != null ? "'" + modelId.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        "MODEL_NAME = " + (modelName != null ? "'" + modelName.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        "UPDATE_TIME = SYSDATE " +
                        "WHERE TASK_ID = " + taskId +
                        " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.toString().replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                        " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.toString().replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                        " AND " + (batchNo != null ? "BATCH_NO = '" + batchNo.toString().replace("'", "''") + "'" : "BATCH_NO IS NULL");

                Util.postCommandSql(updateSql);
                log.info("更新设备信息: " + deviceName);
            } else {
                // 设备不存在，执行插入操作
                String insertSql = "INSERT INTO PANORAMA_DEVICE " +
                        "(DEVICE_ID, TASK_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, MODEL_ID, MODEL_NAME, CREATE_TIME, UPDATE_TIME) " +
                        "VALUES (SEQ_PANORAMA_DEVICE.NEXTVAL, " + taskId + ", " +
                        (deviceName != null ? "'" + deviceName.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (deviceCode != null ? "'" + deviceCode.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (batchNo != null ? "'" + batchNo.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (sequenceNo != null ? sequenceNo.toString() : "NULL") + ", " +
                        (modelId != null ? "'" + modelId.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (modelName != null ? "'" + modelName.toString().replace("'", "''") + "'" : "NULL") + ", SYSDATE, SYSDATE)";

                Util.postCommandSql(insertSql);
                log.info("新增设备信息: " + deviceName);
            }
        } catch (Exception e) {
            log.error("保存单机信息失败", e);
        }
    }

    /**
     * 获取任务下的设备列表（分页）
     */
    public JSONObject getDeviceList(Long taskId, int page, int limit) {
        try {
            // 计算分页参数
            int offset = (page - 1) * limit;

            // 查询总数
            String countSql = "SELECT COUNT(*) as total FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId;
            JSONArray countResult = Util.postQuerySql(countSql);
            JSONArray countArray = countResult;
            int total = 0;
            if (countArray != null && countArray.size() > 0) {
                // 修复类型转换错误：从JSONArray中获取JSONObject
                Object firstRowObj = countArray.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    total = firstRow.getInt("TOTAL");
                }
            }

            // 查询分页数据
            String dataSql = "SELECT * FROM (" +
                    "SELECT ROWNUM rn, t.* FROM (" +
                    "SELECT DEVICE_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, MODEL_ID, MODEL_NAME, " +
                    "TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as CREATE_TIME, " +
                    "TO_CHAR(UPDATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as UPDATE_TIME " +
                    "FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId + " " +
                    "ORDER BY SEQUENCE_NO ASC, CREATE_TIME DESC" +
                    ") t WHERE ROWNUM <= " + (offset + limit) +
                    ") WHERE rn > " + offset;

            JSONArray dataResult = Util.postQuerySql(dataSql);
            JSONArray dataArray = dataResult;

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("count", total)
                    .set("data", dataArray != null ? dataArray : new JSONArray());

        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取设备列表失败: " + e.getMessage())
                    .set("count", 0)
                    .set("data", new JSONArray());
        }
    }

    /**
     * 导出设备数据为Excel文件
     */
    public File exportDeviceExcel(Long taskId) {
        try {
            // 查询任务信息
            String taskSql = "SELECT TASK_NAME FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);
            String taskName = "未知任务";
            if (taskResult != null && taskResult.size() > 0) {
                Object firstRowObj = taskResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    taskName = ((JSONObject) firstRowObj).getStr("TASK_NAME");
                }
            }

            // 查询设备数据
            String deviceSql = "SELECT SEQUENCE_NO, DEVICE_NAME, DEVICE_CODE, BATCH_NO, MODEL_ID, MODEL_NAME " +
                    "FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId + " ORDER BY SEQUENCE_NO ASC, CREATE_TIME ASC";
            JSONArray deviceData = Util.postQuerySql(deviceSql);

            // 准备表头（与模板一致）
            JSONArray headers = new JSONArray();
            headers.add("序号");
            headers.add("单机名称");
            headers.add("单机代号");
            headers.add("批次号");
            headers.add("型号ID");
            headers.add("型号名称");

            // 准备数据
            JSONArray data = new JSONArray();
            if (deviceData != null && deviceData.size() > 0) {
                for (int i = 0; i < deviceData.size(); i++) {
                    Object rowObj = deviceData.get(i);
                    if (rowObj instanceof JSONObject) {
                        JSONObject row = (JSONObject) rowObj;
                        JSONArray dataRow = new JSONArray();

                        // 序号字段确保为整数格式
                        Object sequenceNo = row.get("SEQUENCE_NO");
                        if (sequenceNo != null) {
                            if (sequenceNo instanceof Number) {
                                dataRow.add(((Number) sequenceNo).intValue());
                            } else {
                                try {
                                    dataRow.add(Integer.parseInt(sequenceNo.toString()));
                                } catch (NumberFormatException e) {
                                    dataRow.add(sequenceNo.toString());
                                }
                            }
                        } else {
                            dataRow.add("");
                        }

                        dataRow.add(row.getStr("DEVICE_NAME"));
                        dataRow.add(row.getStr("DEVICE_CODE"));
                        dataRow.add(row.getStr("BATCH_NO"));
                        dataRow.add(row.getStr("MODEL_ID"));
                        dataRow.add(row.getStr("MODEL_NAME"));
                        data.add(dataRow);
                    }
                }
            }

            // 设置列宽
            JSONArray columnWidths = new JSONArray();
            columnWidths.add(8);   // 序号
            columnWidths.add(20);  // 单机名称
            columnWidths.add(15);  // 单机代号
            columnWidths.add(15);  // 批次号
            columnWidths.add(12);  // 型号ID
            columnWidths.add(20);  // 型号名称

            // 生成文件名
            String timestamp = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
            String fileName = "设备数据_" + taskName + "_" + timestamp;

            // 使用CommonUtil.createExcelFile创建Excel文件
            File excelFile = CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 25);

            log.info("导出设备Excel成功: " + excelFile.getAbsolutePath());
            return excelFile;
        } catch (Exception e) {
            log.error("导出设备Excel失败", e);
            return null;
        }
    }

    /**
     * 更新XML文件中的热点信息
     */
    private void updateHotspotInXml(String extractPath, String pan, String tilt, String editedTitle, String editedDescription) {
        try {
            // 查找pano.xml文件
            File panoXmlFile = findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                log.warn("未找到pano.xml文件，无法更新XML中的热点信息");
                return;
            }

            // 读取XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 查找所有panorama节点
            List<Element> panoramas = root.elements("panorama");
            boolean updated = false;
            int matchCount = 0;

            log.info("开始更新XML热点信息 - 定位: pan={}, tilt={}, 新标题: {}", pan, tilt, editedTitle);

            for (Element panorama : panoramas) {
                Element hotspotsElement = panorama.element("hotspots");
                if (hotspotsElement != null) {
                    List<Element> hotspots = hotspotsElement.elements("hotspot");

                    for (Element hotspot : hotspots) {
                        String xmlPan = hotspot.attributeValue("pan");
                        String xmlTilt = hotspot.attributeValue("tilt");
                        String skinid = hotspot.attributeValue("skinid");
                        String currentTitle = hotspot.attributeValue("title");
                        String xmlId = hotspot.attributeValue("id");

                        log.debug("检查热点 - ID: {}, skinid: {}, pan: {}, tilt: {}, title: {}",
                                xmlId, skinid, xmlPan, xmlTilt, currentTitle);

                        // 使用pan、tilt和skinid进行精确匹配
                        if ("stand-alone".equals(skinid) &&
                            pan != null && pan.equals(xmlPan) &&
                            tilt != null && tilt.equals(xmlTilt)) {

                            // 更新title和description属性
                            if (editedTitle != null && !editedTitle.trim().isEmpty()) {
                                hotspot.addAttribute("title", editedTitle);
                                log.info("更新热点标题: {} -> {} (定位: pan={}, tilt={})",
                                        currentTitle, editedTitle, pan, tilt);
                            }
                            if (editedDescription != null && !editedDescription.trim().isEmpty()) {
                                hotspot.addAttribute("description", editedDescription);
                                log.info("更新热点描述: {} (定位: pan={}, tilt={})",
                                        editedDescription, pan, tilt);
                            }
                            updated = true;
                            matchCount++;
                            log.info("已更新XML中热点 - 定位: pan={}, tilt={}", pan, tilt);
                            // 理论上pan+tilt组合是唯一的，但为了安全起见，继续检查
                        }
                    }
                }
            }

            if (updated) {
                // 写回XML文件
                writeXmlToFile(document, panoXmlFile);
                log.info("XML文件已更新: {}, 共更新 {} 个热点", panoXmlFile.getAbsolutePath(), matchCount);
            } else {
                log.warn("未找到XML中的热点 - 定位: pan={}, tilt={}", pan, tilt);
            }

        } catch (Exception e) {
            log.error("更新XML文件中的热点信息失败", e);
        }
    }

    /**
     * 将Document写入XML文件
     */
    private void writeXmlToFile(Document document, File xmlFile) throws Exception {
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");

        try (FileOutputStream fos = new FileOutputStream(xmlFile)) {
            XMLWriter writer = new XMLWriter(fos, format);
            writer.write(document);
            writer.close();
        }
    }

    /**
     * 获取热点列表
     * @param taskId 任务ID
     * @param panoramaId 全景节点ID（可选，用于多节点支持）
     * @param page 页码
     * @param limit 每页数量
     * @return 热点列表
     */
    public JSONObject getHotspotList(Long taskId, String panoramaId, Integer page, Integer limit) {
        try {
            int offset = (page - 1) * limit;

            // 构建WHERE条件，支持panoramaId过滤
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);
            if (panoramaId != null && !panoramaId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(panoramaId.replace("'", "''")).append("'");
                log.debug("按panoramaId过滤热点列表: taskId={}, panoramaId={}", taskId, panoramaId);
            } else {
                log.debug("获取任务下所有热点列表: taskId={}", taskId);
            }

            // 查询总数
            String countSql = "SELECT COUNT(*) AS TOTAL FROM PANORAMA_HOTSPOT h " + whereCondition.toString();
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            // 查询分页数据，包含PANORAMA_ID字段
            String dataSql = "SELECT * FROM (" +
                    "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                    "h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, h.EDITED_TITLE, h.EDITED_DESCRIPTION, " +
                    "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                    "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME, " +
                    "ROW_NUMBER() OVER (ORDER BY h.CREATE_TIME) AS RN " +
                    "FROM PANORAMA_HOTSPOT h " +
                    "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                    whereCondition.toString() + ") " +
                    "WHERE RN > " + offset + " AND RN <= " + (offset + limit);

            JSONArray dataResult = Util.postQuerySql(dataSql);

            log.debug("热点列表查询完成: taskId={}, panoramaId={}, total={}, page={}, limit={}",
                     taskId, panoramaId, total, page, limit);

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("data", dataResult)
                    .set("count", total);
        } catch (Exception e) {
            log.error("获取热点列表失败: taskId={}, panoramaId={}", taskId, panoramaId, e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取热点列表失败: " + e.getMessage())
                    .set("data", new JSONArray())
                    .set("count", 0);
        }
    }

    /**
     * 获取当前全景节点ID
     * @param taskId 任务ID
     * @return 当前节点信息
     */
    public JSONObject getCurrentNode(Long taskId) {
        try {
            // 获取任务的XML文件路径
            String taskSql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);

            if (taskResult == null || taskResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            JSONObject taskInfo = (JSONObject) taskResult.get(0);
            String extractPath = taskInfo.getStr("EXTRACT_PATH");

            if (extractPath == null || extractPath.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务没有XML文件路径");
            }

            // 查找并解析XML文件
            File panoXmlFile = findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到pano.xml文件");
            }

            // 解析XML获取当前节点
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 获取tour节点的start属性
            Element tourElement = root.element("tour");
            String currentNodeId = "node1"; // 默认值

            if (tourElement != null) {
                String startAttribute = tourElement.attributeValue("start");
                if (startAttribute != null && !startAttribute.trim().isEmpty()) {
                    currentNodeId = startAttribute;
                }
            }

            log.debug("获取当前节点: taskId={}, currentNodeId={}", taskId, currentNodeId);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj()
                            .set("currentNodeId", currentNodeId)
                            .set("taskId", taskId));

        } catch (Exception e) {
            log.error("获取当前节点失败: taskId={}", taskId, e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取当前节点失败: " + e.getMessage());
        }
    }

    /**
     * 更新热点信息
     */
    public JSONObject updateHotspot(Long hotspotId, String editedTitle, String editedDescription, Long deviceId) {
        try {
            // 首先获取热点信息，包括任务ID、定位信息（pan、tilt）和原始标题
            String querySql = "SELECT h.TASK_ID, h.HOTSPOT_XML_ID, h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, " +
                    "h.PAN, h.TILT, t.EXTRACT_PATH FROM PANORAMA_HOTSPOT h " +
                    "JOIN PANORAMA_TASK t ON h.TASK_ID = t.TASK_ID " +
                    "WHERE h.HOTSPOT_ID = " + hotspotId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到热点信息");
            }

            JSONObject hotspotInfo = (JSONObject) queryResult.get(0);
            Long taskId = hotspotInfo.getLong("TASK_ID");
            String hotspotXmlId = hotspotInfo.getStr("HOTSPOT_XML_ID");
            String originalTitle = hotspotInfo.getStr("ORIGINAL_TITLE");
            String pan = hotspotInfo.getStr("PAN");
            String tilt = hotspotInfo.getStr("TILT");
            String extractPath = hotspotInfo.getStr("EXTRACT_PATH");

            // 更新数据库
            StringBuilder updateSql = new StringBuilder("UPDATE PANORAMA_HOTSPOT SET ");
            boolean hasUpdate = false;

            if (editedTitle != null) {
                updateSql.append("EDITED_TITLE = '").append(editedTitle.replace("'", "''")).append("'");
                hasUpdate = true;
            }

            if (editedDescription != null) {
                if (hasUpdate) updateSql.append(", ");
                updateSql.append("EDITED_DESCRIPTION = '").append(editedDescription.replace("'", "''")).append("'");
                hasUpdate = true;
            }

            if (deviceId != null) {
                if (hasUpdate) updateSql.append(", ");
                updateSql.append("DEVICE_ID = ").append(deviceId);
                hasUpdate = true;
            }

            if (hasUpdate) {
                updateSql.append(", IS_EDITED = 1, UPDATE_TIME = SYSDATE");
                updateSql.append(" WHERE HOTSPOT_ID = ").append(hotspotId);

                Util.postCommandSql(updateSql.toString());

                // 更新XML文件中的热点信息，使用定位信息（pan、tilt）进行精确匹配
                if (extractPath != null && pan != null && tilt != null) {
                    updateHotspotInXml(extractPath, pan, tilt, editedTitle, editedDescription);
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "热点信息更新成功");
        } catch (Exception e) {
            log.error("更新热点信息失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "更新热点信息失败: " + e.getMessage());
        }
    }



    /**
     * 热点定位
     */
    public JSONObject locateHotspot(Long hotspotId) {
        try {
            String sql = "SELECT PAN, TILT FROM PANORAMA_HOTSPOT WHERE HOTSPOT_ID = " + hotspotId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "热点不存在");
            }

            // 安全获取热点数据
            Object firstRowObj = result.get(0);
            JSONObject hotspot = null;
            if (firstRowObj instanceof JSONObject) {
                hotspot = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspot);
        } catch (Exception e) {
            log.error("热点定位失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "热点定位失败: " + e.getMessage());
        }
    }

    /**
     * 根据热点信息查找热点记录
     * 优先使用坐标匹配，确保准确性
     */
    public JSONObject findHotspotByInfo(Long taskId, String nodeId, String hotspotId,
                                       String title, String description, String skinid) {
        try {
            log.debug("查找热点记录: taskId={}, nodeId={}, hotspotId={}, title={}, skinid={}",
                     taskId, nodeId, hotspotId, title, skinid);

            // 构建基础WHERE条件
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);

            // 添加节点ID过滤（如果提供）
            if (nodeId != null && !nodeId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(nodeId.replace("'", "''")).append("'");
            }

            // 构建多重匹配条件（按优先级排序）
            StringBuilder matchConditions = new StringBuilder();

            // 优先级1: 使用原始标题匹配（最可靠，因为标题在节点内通常是唯一的）
            if (title != null && !title.trim().isEmpty()) {
                matchConditions.append("h.ORIGINAL_TITLE = '").append(title.replace("'", "''")).append("'");
            }

            // 优先级2: 使用XML ID匹配（结合节点ID）
            if (hotspotId != null && !hotspotId.trim().isEmpty()) {
                if (matchConditions.length() > 0) {
                    matchConditions.append(" OR ");
                }
                matchConditions.append("h.HOTSPOT_XML_ID = '").append(hotspotId.replace("'", "''")).append("'");
            }

            // 优先级3: 使用皮肤ID匹配（结合其他条件）
            if (skinid != null && !skinid.trim().isEmpty()) {
                if (matchConditions.length() > 0) {
                    matchConditions.append(" OR ");
                }
                matchConditions.append("h.SKINID = '").append(skinid.replace("'", "''")).append("'");
            }

            // 如果没有任何匹配条件，返回错误
            if (matchConditions.length() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "缺少有效的热点标识信息");
            }

            // 构建完整的查询SQL
            String sql = "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                        "h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, h.EDITED_TITLE, h.EDITED_DESCRIPTION, " +
                        "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                        "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME " +
                        "FROM PANORAMA_HOTSPOT h " +
                        "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                        whereCondition.toString() + " AND (" + matchConditions.toString() + ") " +
                        "ORDER BY " +
                        "CASE " +
                        "  WHEN h.ORIGINAL_TITLE = '" + (title != null ? title.replace("'", "''") : "") + "' THEN 1 " +
                        "  WHEN h.HOTSPOT_XML_ID = '" + (hotspotId != null ? hotspotId.replace("'", "''") : "") + "' THEN 2 " +
                        "  ELSE 3 " +
                        "END";

            log.debug("执行热点查找SQL: {}", sql);
            JSONArray result = Util.postQuerySql(sql);

            if (result == null || result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到匹配的热点记录");
            }

            // 返回第一个匹配的记录（优先级最高的）
            Object firstRowObj = result.get(0);
            JSONObject hotspotData = null;
            if (firstRowObj instanceof JSONObject) {
                hotspotData = (JSONObject) firstRowObj;
            }

            log.info("成功找到热点记录: hotspotId={}, title={}",
                    hotspotData.getStr("HOTSPOT_XML_ID"), hotspotData.getStr("ORIGINAL_TITLE"));

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspotData)
                    .set("msg", "查找热点成功");

        } catch (Exception e) {
            log.error("查找热点记录失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "查找热点记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据坐标查找热点记录（最可靠的匹配方式）
     */
    public JSONObject findHotspotByCoordinates(Long taskId, String nodeId, String pan, String tilt) {
        try {
            log.debug("根据坐标查找热点记录: taskId={}, nodeId={}, pan={}, tilt={}",
                     taskId, nodeId, pan, tilt);

            // 构建基础WHERE条件
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);

            // 添加节点ID过滤（如果提供）
            if (nodeId != null && !nodeId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(nodeId.replace("'", "''")).append("'");
            }

            // 添加坐标匹配条件（精确匹配）
            if (pan != null && tilt != null) {
                whereCondition.append(" AND h.PAN = '").append(pan.replace("'", "''")).append("'");
                whereCondition.append(" AND h.TILT = '").append(tilt.replace("'", "''")).append("'");
            } else {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "缺少坐标信息");
            }

            // 构建完整的查询SQL
            String sql = "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                        "h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, h.EDITED_TITLE, h.EDITED_DESCRIPTION, " +
                        "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                        "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME " +
                        "FROM PANORAMA_HOTSPOT h " +
                        "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                        whereCondition.toString();

            log.debug("执行坐标查找SQL: {}", sql);
            JSONArray result = Util.postQuerySql(sql);

            if (result == null || result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到匹配的热点记录");
            }

            // 返回匹配的记录
            Object firstRowObj = result.get(0);
            JSONObject hotspotData = null;
            if (firstRowObj instanceof JSONObject) {
                hotspotData = (JSONObject) firstRowObj;
            }

            log.info("根据坐标成功找到热点记录: pan={}, tilt={}, title={}",
                    pan, tilt, hotspotData.getStr("ORIGINAL_TITLE"));

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspotData)
                    .set("msg", "查找热点成功");

        } catch (Exception e) {
            log.error("根据坐标查找热点记录失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "根据坐标查找热点记录失败: " + e.getMessage());
        }
    }

    /**
     * 导出修改后的全景图包
     */
    public String exportPanorama(Long taskId) {
        try {
            // 1. 获取任务信息
            String querySql = "SELECT TASK_NAME, EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                throw new RuntimeException("任务不存在");
            }

            JSONObject taskInfo = (JSONObject) queryResult.get(0);
            String taskName = taskInfo.getStr("TASK_NAME");
            String extractPath = taskInfo.getStr("EXTRACT_PATH");

            if (extractPath == null || extractPath.trim().isEmpty()) {
                throw new RuntimeException("任务尚未上传全景图文件");
            }

            File extractDir = new File(extractPath);
            if (!extractDir.exists() || !extractDir.isDirectory()) {
                throw new RuntimeException("全景图文件目录不存在");
            }

            // 2. 创建导出目录和ZIP文件名
            String exportDir = tempPath + File.separator + "panorama_export_" + System.currentTimeMillis();
            FileUtil.mkdir(exportDir);

            String zipFileName = (taskName != null ? taskName : "panorama_task_" + taskId) + "_exported.zip";
            String zipFilePath = exportDir + File.separator + zipFileName;

            try {
                log.info("开始清理热点定位脚本: {}", extractPath);
                Pano2VRHotspotInjector.cleanDirectory(extractPath);
                log.info("热点定位脚本清理完成");
            } catch (Exception e) {
                log.warn("清理热点定位脚本失败，但不影响导出流程: {}", e.getMessage());
            }

            // 4. 将解压目录重新打包成ZIP文件
            log.info("开始打包全景图文件 - 源目录: {}, 目标文件: {}", extractPath, zipFilePath);
            ZipUtil.zip(extractPath, zipFilePath);

            // 5. 验证ZIP文件是否创建成功
            File zipFile = new File(zipFilePath);
            if (!zipFile.exists() || zipFile.length() == 0) {
                throw new RuntimeException("ZIP文件创建失败");
            }

            log.info("全景图包导出成功 - 文件: {}, 大小: {} bytes", zipFilePath, zipFile.length());

            // 6. 重新注入热点定位脚本（保持编辑功能可用）
            try {
                log.info("重新注入热点定位脚本: {}", extractPath);
                Pano2VRHotspotInjector.processDirectory(extractPath);
                log.info("热点定位脚本重新注入完成");
            } catch (Exception e) {
                log.warn("重新注入热点定位脚本失败: {}", e.getMessage());
            }

            // 7. 更新任务状态为已导出
            String updateSql = "UPDATE PANORAMA_TASK SET STATUS = 2, UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateSql);

            return zipFilePath;

        } catch (Exception e) {
            log.error("导出全景图包失败", e);
            throw new RuntimeException("导出全景图包失败: " + e.getMessage());
        }
    }

    /**
     * 获取全景图预览路径
     */
    public JSONObject getPreviewPath(Long taskId) {
        try {
            String sql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            // 安全获取提取路径
            String extractPath = null;
            if (result.size() > 0) {
                Object firstRowObj = result.get(0);
                if (firstRowObj instanceof JSONObject) {
                    extractPath = ((JSONObject) firstRowObj).getStr("EXTRACT_PATH");
                }
            }
            if (extractPath == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "尚未上传全景图文件");
            }

            // 查找index.html文件
            File indexFile = new File(extractPath + File.separator + "index.html");
            if (!indexFile.exists()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到index.html文件");
            }

            // 返回相对于webapps的路径
            String relativePath = extractPath.replace(fileUploadPath, "");
            String previewUrl = "/file/preview" + relativePath + "/index.html";

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj().set("previewUrl", previewUrl));
        } catch (Exception e) {
            log.error("获取预览路径失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取预览路径失败: " + e.getMessage());
        }
    }

    /**
     * 测试数据库连接和表结构
     */
    public JSONObject testDatabase() {
        try {
            JSONObject result = JSONUtil.createObj();

            // 测试表是否存在
            try {
                String testTableSql = "SELECT COUNT(*) AS CNT FROM USER_TABLES WHERE TABLE_NAME = 'PANORAMA_TASK'";
                JSONArray tableResult = Util.postQuerySql(testTableSql);
                boolean tableExists = false;
                if (tableResult != null && tableResult.size() > 0) {
                    Object firstRowObj = tableResult.get(0);
                    if (firstRowObj instanceof JSONObject) {
                        tableExists = ((JSONObject) firstRowObj).getInt("CNT") > 0;
                    }
                }
                result.set("tableExists", tableExists);

                if (tableExists) {
                    // 测试查询所有任务
                    String allTasksSql = "SELECT * FROM PANORAMA_TASK";
                    JSONArray allTasks = Util.postQuerySql(allTasksSql);
                    result.set("allTasks", allTasks);
                    result.set("taskCount", allTasks.size());

                    // 测试序列是否存在
                    String seqSql = "SELECT SEQ_PANORAMA_TASK.NEXTVAL FROM DUAL";
                    JSONArray seqResult = Util.postQuerySql(seqSql);
                    result.set("sequenceWorks", true);
                    if (seqResult != null && seqResult.size() > 0) {
                        Object firstRowObj = seqResult.get(0);
                        if (firstRowObj instanceof JSONObject) {
                            result.set("nextTaskId", ((JSONObject) firstRowObj).getLong("NEXTVAL"));
                        }
                    }
                } else {
                    result.set("error", "PANORAMA_TASK表不存在");
                }

            } catch (Exception e) {
                result.set("tableError", e.getMessage());
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", result);
        } catch (Exception e) {
            log.error("测试数据库失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "测试数据库失败: " + e.getMessage());
        }
    }
}
