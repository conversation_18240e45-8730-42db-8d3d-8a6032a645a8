/**
* @definition    QueryStandAloneByModel
* @description   用于全景图中查询单机信息 wanghq 2025年6月9日14:06:57
* @implementation    {Script}
*
* @param    {NUMBER}    modelId    
*
* @returns    {JSON}
*/
var res = {};
try {
    logger.info("QueryStandAloneByModel - 开始查询单机信息，modelId: " + modelId);

    //查询单机配置表的信息
    var queryTableSql = "select * from TABLE_CONFIG where TREE_NAME = '单机拆装'";
    logger.debug("QueryStandAloneByModel - 查询表配置SQL: " + queryTableSql);
    var table = Things['Thing.DB.Oracle'].RunQuery({ sql: queryTableSql }).rows[0];

    var tableName = table.TABLE_NAME;
    var tableId = table.ID;
    logger.info("QueryStandAloneByModel - 使用表名: " + tableName + ", 表ID: " + tableId);

    //查询参数信息
    var queryParamSql = "select * from PARAM_CONFIG where TABLE_ID=" + tableId;
    logger.debug("QueryStandAloneByModel - 查询参数配置SQL: " + queryParamSql);
    var params = Things['Thing.DB.Oracle'].RunQuery({ sql: queryParamSql }).rows;
    logger.debug("QueryStandAloneByModel - 查询到参数配置数量: " + params.length);

    var nameColName, codeColName, batchColName;
    for (var i = 0; i < params.length; i++) {
        var param = params[i];
        var colName = param['COL_NAME'];
        var interfaceName = param['INTERFACE_NAME'];
        logger.debug("QueryStandAloneByModel - 处理参数配置: colName=" + colName + ", interfaceName=" + interfaceName);
        if (interfaceName == 'OBJECT_NAME') {
            //名称
            nameColName = colName;
            logger.debug("QueryStandAloneByModel - 设置名称列名: " + nameColName);
        } else if (interfaceName == 'OBJECT_CODE') {
            codeColName = colName;
            logger.debug("QueryStandAloneByModel - 设置代码列名: " + codeColName);
        } else if (interfaceName == 'OBJECT_BATCH') {
            batchColName = colName;
            logger.debug("QueryStandAloneByModel - 设置批次列名: " + batchColName);
        }
    }

    logger.info("QueryStandAloneByModel - 列名映射完成: nameColName=" + nameColName + ", codeColName=" + codeColName + ", batchColName=" + batchColName);

    var querySql = "select * from " + tableName + " where TREE_ID in (select treeid from datapackagetree start with treeid = " + modelId + " connect by prior treeid = parentid)";
    logger.debug("QueryStandAloneByModel - 查询单机数据SQL: " + querySql);
    var datas = Things['Thing.DB.Oracle'].RunQuery({ sql: querySql }).ToJSON().rows;
    logger.info("QueryStandAloneByModel - 查询到单机数据数量: " + datas.length);

    var resData = [];
    for (var i = 0; i < datas.length; i++) {
        var data = datas[i];
        var deviceName = data[nameColName];
        var deviceCode = data[codeColName];
        var deviceBatch = data[batchColName];
        logger.debug("QueryStandAloneByModel - 处理单机数据[" + i + "]: deviceName=" + deviceName + ", deviceCode=" + deviceCode + ", deviceBatch=" + deviceBatch);
        resData.push({
            devicename: deviceName,
            devicecode: deviceCode,
            devicebatch: deviceBatch
        });
    }

    logger.info("QueryStandAloneByModel - 数据处理完成，返回设备数量: " + resData.length);

    res.success = true;
    res.data = resData;
    res.msg = "QueryStandAloneByModel-查询单机信息成功";
    
} catch (error) {
    res.success = false;
    res.msg = "QueryStandAloneByModel-查询单机信息失败，原因：" + error;
    logger.error("QueryStandAloneByModel - " + res.msg);
    logger.error("QueryStandAloneByModel - 错误堆栈: " + error.stack);
}
result = res;