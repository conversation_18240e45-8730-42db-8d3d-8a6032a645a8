/**
 * Pano2VR热点定位与节点切换监听脚本
 *
 * 此脚本用于：
 * 1. 接收来自父页面的定位指令并调用pano2vr的JavaScript API实现热点定位功能
 * 2. 监听全景图节点切换事件，支持多节点全景图功能
 *
 * <AUTHOR>
 * @date 2025-06-06
 * @version 2.0 - 增加多节点支持
 */

(function() {
    'use strict';

    // 全局变量
    var currentNodeId = null; // 当前节点ID
    var isNodeSwitchListenerInitialized = false; // 节点切换监听器是否已初始化

    // 等待pano2vr加载完成
    var waitForPano = function(callback) {
        var checkInterval = setInterval(function() {
            // 检查pano对象是否存在且已初始化
            if (typeof pano !== 'undefined' && pano && typeof pano.moveTo === 'function') {
                clearInterval(checkInterval);
                callback();
            }
        }, 100); // 每100ms检查一次

        // 10秒超时
        setTimeout(function() {
            clearInterval(checkInterval);
        }, 10000);
    };

    // 获取当前节点ID
    var getCurrentNodeId = function() {
        try {
            // 尝试多种方法获取当前节点ID
            if (typeof pano !== 'undefined' && pano) {
                // 方法1: 直接获取当前节点ID（如果Pano2VR提供此API）
                if (typeof pano.getCurrentNode === 'function') {
                    return pano.getCurrentNode();
                }

                // 方法2: 通过URL参数获取
                if (typeof pano.getURL === 'function') {
                    var url = pano.getURL();
                    var match = url.match(/node=([^&]+)/);
                    if (match) {
                        return match[1];
                    }
                }

                // 方法3: 通过变量获取（如果Pano2VR设置了全局变量）
                if (typeof window.currentNode !== 'undefined') {
                    return window.currentNode;
                }

                // 方法4: 检查DOM中的节点信息
                var nodeElements = document.querySelectorAll('[data-node-id]');
                if (nodeElements.length > 0) {
                    return nodeElements[0].getAttribute('data-node-id');
                }
            }

            // 默认返回node1
            return 'node1';
        } catch (error) {
            return 'node1';
        }
    };

    // 初始化节点切换监听器
    var initNodeSwitchListener = function() {
        if (isNodeSwitchListenerInitialized) {
            return; // 避免重复初始化
        }

        try {
            // 获取初始节点ID
            currentNodeId = getCurrentNodeId();

            // 向父页面发送初始节点信息
            notifyParentNodeSwitch(currentNodeId, 'initial');

            // 方法1: 监听Pano2VR的节点切换事件（如果提供）
            if (typeof pano !== 'undefined' && pano && typeof pano.addEventListener === 'function') {
                pano.addEventListener('nodechange', function(event) {
                    handleNodeSwitch(event.nodeId || getCurrentNodeId());
                });
            }

            // 方法2: 定期检查节点变化（轮询方式）
            var lastNodeId = currentNodeId;
            var nodeCheckInterval = setInterval(function() {
                var newNodeId = getCurrentNodeId();
                if (newNodeId !== lastNodeId) {
                    handleNodeSwitch(newNodeId);
                    lastNodeId = newNodeId;
                }
            }, 500); // 每500ms检查一次

            // 方法3: 监听URL变化（如果节点切换会改变URL）
            var lastUrl = window.location.href;
            var urlCheckInterval = setInterval(function() {
                var currentUrl = window.location.href;
                if (currentUrl !== lastUrl) {
                    lastUrl = currentUrl;
                    var newNodeId = getCurrentNodeId();
                    if (newNodeId !== currentNodeId) {
                        handleNodeSwitch(newNodeId);
                    }
                }
            }, 300); // 每300ms检查一次

            isNodeSwitchListenerInitialized = true;

        } catch (error) {
            // 初始化节点切换监听器失败
        }
    };

    // 初始化热点点击监听器
    var initHotspotClickListener = function() {
        try {
            // 监听Pano2VR的节点切换事件，在每次节点切换时重新绑定热点点击事件
            if (typeof pano !== 'undefined' && pano && typeof pano.addListener === 'function') {
                pano.addListener('changenode', function(args) {
                    // 延迟一下确保热点已经加载完成
                    setTimeout(function() {
                        bindHotspotClickEvents();
                    }, 500);
                });
            }

            // 初始绑定热点点击事件
            setTimeout(function() {
                bindHotspotClickEvents();
            }, 1000);

        } catch (error) {
            // 初始化热点点击监听器失败
        }
    };

    // 绑定热点点击事件
    var bindHotspotClickEvents = function() {
        try {
            // 获取当前页面的所有热点
            if (typeof pano !== 'undefined' && pano && pano.P) {
                var hotspots = pano.P;

                for (var i = 0; i < hotspots.length; i++) {
                    var hotspot = hotspots[i];

                    // 获取热点的DOM元素
                    if (hotspot.b && hotspot.b.__div) {
                        var divElement = hotspot.b.__div;

                        // 移除之前的点击事件监听器（避免重复绑定）
                        divElement.removeEventListener('click', hotspotClickHandler);

                        // 将热点信息存储到DOM元素的data属性中
                        divElement.hotspotData = {
                            id: hotspot.id || '',
                            title: hotspot.title || '',
                            description: hotspot.description || '',
                            skinid: hotspot.skinid || '',
                            url: hotspot.url || '',
                            target: hotspot.target || '',
                            pan: hotspot.pan || '',
                            tilt: hotspot.tilt || ''
                        };

                        // 绑定点击事件
                        divElement.addEventListener('click', hotspotClickHandler);
                    }
                }
            }
        } catch (error) {
            // 绑定热点点击事件失败
        }
    };

    // 热点点击事件处理器
    var hotspotClickHandler = function(event) {
        try {
            // 阻止事件冒泡，避免触发其他点击事件
            event.stopPropagation();

            // 获取热点数据
            var hotspotData = this.hotspotData;
            if (!hotspotData) {
                return;
            }

            // 向父页面发送热点点击消息
            var message = {
                type: 'hotspotClick',
                hotspot: hotspotData,
                nodeId: currentNodeId,
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');

        } catch (error) {
            // 热点点击处理失败
        }
    };

    // 处理节点切换
    var handleNodeSwitch = function(newNodeId) {
        if (newNodeId && newNodeId !== currentNodeId) {
            currentNodeId = newNodeId;
            notifyParentNodeSwitch(newNodeId, 'switch');
        }
    };

    // 向父页面发送节点切换消息
    var notifyParentNodeSwitch = function(nodeId, type) {
        try {
            var message = {
                type: 'nodeSwitch',
                nodeId: nodeId,
                switchType: type, // 'initial' 或 'switch'
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');
        } catch (error) {
            // 发送节点切换消息失败
        }
    };

    // 初始化消息监听器
    var initMessageListener = function() {
        window.addEventListener('message', function(event) {
            // 安全检查：验证消息来源（可根据需要调整）
            // if (event.origin !== window.location.origin) return;
            
            var data = event.data;
            if (!data || typeof data !== 'object') return;
            
            // 处理热点定位消息
            if (data.type === 'locateHotspot') {
                handleHotspotLocation(data);
            }
            // 处理热点更新消息
            else if (data.type === 'updateHotspot') {
                handleHotspotUpdate(data);
            }
        }, false);
    };
    
    // 处理热点定位
    var handleHotspotLocation = function(data) {
        try {
            var pan = parseFloat(data.pan);
            var tilt = parseFloat(data.tilt);
            var speed = parseFloat(data.speed) || 2.0; // 默认速度
            
            // 验证数据
            if (isNaN(pan) || isNaN(tilt)) {
                return;
            }
            
            // 获取当前FOV，保持不变
            var currentFov = pano.getFov();
            
            // 使用moveTo方法实现平滑过渡
            // moveTo(pan, tilt, fov, speed, roll, projection)
            pano.moveTo(pan, tilt, currentFov, speed);
            
            // 向父页面发送定位完成消息
            setTimeout(function() {
                try {
                    window.parent.postMessage({
                        type: 'hotspotLocationComplete',
                        pan: pan,
                        tilt: tilt,
                        success: true
                    }, '*');
                } catch (e) {
                    // 静默处理错误
                }
            }, speed * 1000 + 100); // 等待动画完成后发送消息
            
        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotLocationComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 处理热点更新
    var handleHotspotUpdate = function(data) {
        try {
            if (!data || !data.hotspot) {
                return;
            }

            var updateInfo = data.hotspot;
            var pan = updateInfo.pan;
            var tilt = updateInfo.tilt;
            var newTitle = updateInfo.title;
            var newDescription = updateInfo.description;

            // 获取当前页面的所有热点
            if (typeof pano !== 'undefined' && pano && pano.P) {
                var hotspots = pano.P;
                var updated = false;

                for (var i = 0; i < hotspots.length; i++) {
                    var hotspot = hotspots[i];

                    // 通过坐标匹配热点（最可靠的方式）
                    if (hotspot.pan && hotspot.tilt &&
                        Math.abs(parseFloat(hotspot.pan) - parseFloat(pan)) < 0.01 &&
                        Math.abs(parseFloat(hotspot.tilt) - parseFloat(tilt)) < 0.01) {

                        // 更新热点属性
                        if (newTitle !== undefined && newTitle !== null) {
                            hotspot.title = newTitle;
                        }
                        if (newDescription !== undefined && newDescription !== null) {
                            hotspot.description = newDescription;
                        }

                        // 更新DOM元素中的显示文本
                        if (hotspot.b && hotspot.b.__div) {
                            var divElement = hotspot.b.__div;

                            // 更新存储的数据
                            if (divElement.hotspotData) {
                                if (newTitle !== undefined && newTitle !== null) {
                                    divElement.hotspotData.title = newTitle;
                                }
                                if (newDescription !== undefined && newDescription !== null) {
                                    divElement.hotspotData.description = newDescription;
                                }
                            }

                            // 更新DOM中的文本显示并重新居中
                            var textElement = divElement.querySelector('.ggskin.ggskin_text > div');
                            if (textElement && newTitle !== undefined && newTitle !== null) {
                                textElement.textContent = newTitle;

                                // 重新计算文本居中位置
                                adjustHotspotTextPosition(divElement, textElement);
                            }
                        }

                        updated = true;
                        break; // 找到匹配的热点后退出循环
                    }
                }

                // 向父页面发送更新完成消息
                var message = {
                    type: 'hotspotUpdateComplete',
                    success: updated,
                    pan: pan,
                    tilt: tilt,
                    timestamp: new Date().getTime()
                };

                window.parent.postMessage(message, '*');

            }
        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotUpdateComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 调整热点文本位置，确保居中显示
    var adjustHotspotTextPosition = function(hotspotDiv, textElement) {
        try {
            // 等待DOM更新完成后再计算
            setTimeout(function() {
                // 获取文本容器
                var textContainer = textElement.parentElement;
                if (!textContainer) return;

                // 临时设置文本容器为自动宽度以获取真实文本宽度
                textContainer.style.width = 'auto';
                textContainer.style.whiteSpace = 'nowrap';
                textElement.style.width = 'auto';
                textElement.style.left = '0px';

                // 强制重新计算布局
                textContainer.offsetWidth;

                // 获取文本的实际宽度
                var textWidth = textElement.offsetWidth;

                // 计算居中位置
                // 热点图标是32px宽，居中位置是-16px
                // 文本容器需要相对于热点中心居中
                var containerLeft = -(textWidth / 2);

                // 设置文本容器的最终位置和宽度
                textContainer.style.left = containerLeft + 'px';
                textContainer.style.width = textWidth + 'px';

                // 确保文本在容器内居中
                textElement.style.left = '0px';
                textElement.style.width = textWidth + 'px';
                textElement.style.textAlign = 'center';

            }, 100); // 延迟100ms确保DOM更新完成

        } catch (error) {
            // 调整位置失败，静默处理
        }
    };

    // 初始化所有功能
    var initAllFeatures = function() {
        initMessageListener();
        initNodeSwitchListener();
        initHotspotClickListener();
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            waitForPano(initAllFeatures);
        });
    } else {
        waitForPano(initAllFeatures);
    }

    // 为了兼容性，也在window.onload时尝试初始化
    var originalOnload = window.onload;
    window.onload = function() {
        if (originalOnload) originalOnload();
        waitForPano(initAllFeatures);
    };
    
})();
