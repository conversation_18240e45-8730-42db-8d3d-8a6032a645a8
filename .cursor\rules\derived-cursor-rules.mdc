---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## Headers

This document defines the rules and guidelines for the AI coding assistant in this project. It covers coding standards, project-specific conventions, debugging, workflow, and other relevant practices.

## Project Description & Conventions
- The project involves managing data packages, potentially including components for AIT screens and BPM workflows.
- File names and directory structures mentioned in user requests should be adhered to. Pay close attention to files like `nonconformity-chart.js`, `QueryNonconformityList.js`, `create_bpm.sql`, and directories like `twx/Thing.Fn.BPM`.
- When generating code, refer to existing files (e.g., `panorama-editor.html`, `项目开发总结_全景图热点编辑系统.md`) for context and guidance.
- When asked to summarize a file or page, include all relevant file paths and descriptions to guide future AI coding conversations.

## TECH STACK
- JavaScript (ES5 and up)
- Spring Boot
- Java
- HTML
- CSS
- Lombok
- Oracle SQL
- Maven
- Bootstrap
- LayUI
- jQuery 3.x
- Apache Commons
- Hutool
- jQuery FileDownload plugin

## Coding Standards

### JavaScript
- Follow established JavaScript conventions.
- Ensure code is compatible with ES5 unless explicitly instructed otherwise.
- When modifying existing functions, maintain their original structure and purpose.
- **ES5 Syntax Requirements:**
  - Use `var` for variable declarations.
  - Use `response.data || []` for handling potentially undefined data.
  - Use standard string concatenation instead of template literals.
  - Avoid ES6+ features like `const`, `let`, arrow functions, destructuring, and `Promise/async/await`.

### Java
- Adhere to standard Java coding conventions.
- Use Lombok annotations (e.g., `@Slf4j`) for logging where appropriate.

### SQL
- Write standard Oracle SQL.
- When creating or modifying SQL scripts, include comments to explain the purpose and logic.
- Ensure SQL scripts handle potential errors and edge cases gracefully.

### General
- Use clear and descriptive names for variables, functions, and classes.
- Comment code adequately to explain its functionality.
- Handle potential errors gracefully with appropriate error messages.
- Follow KISS (Keep It Simple, Stupid) and YAGNI (You Ain't Gonna Need It) principles.
- Prioritize code readability.
- Each function/method should have a single responsibility.

## User Interface (UI) Guidelines

### General
- Aim for clean, professional designs suitable for office environments.
- Prioritize clarity and readability.
- Adhere to established UI patterns and conventions.
- For responsive designs, consider resolutions 1920x1080 and 1366x768.

### CSS
- Utilize a consistent CSS design system with tokens for colors, fonts, spacing, and other style attributes.
- Ensure responsiveness across different screen sizes (desktop, tablet, mobile).
- Optimize CSS for performance (e.g., minimize file sizes, use hardware acceleration).
- Be mindful of accessibility (e.g., high contrast, keyboard navigation).
- Use Bootstrap grid system for layout.
- For custom styles, use BEM naming convention.

### Specific UI Elements
- **Tables:** Use clear borders and appropriate spacing for readability.
- **Buttons:** Provide clear visual feedback on hover and click.
- **Forms:** Align labels and inputs properly.

## Workflow & Release Rules

- Follow a well-defined workflow for development, testing, and deployment.
- Implement a code review process to ensure quality and adherence to standards.
- Use a version control system (e.g., Git) to manage code changes.
- Create a detailed README file for each service or component, explaining its purpose, usage, and configuration.
- If introducing a new library, make sure to check its license is compatible with the project.
- Before releasing a new version, back up the current version.
- Database script changes require a rollback plan.
- Use gradual rollout for production environment deployments.

## DEBUGGING

- Implement comprehensive logging using `@Slf4j` in Java and `console.log` or similar in JavaScript.
- Include informative messages to track the flow of execution and potential errors.
- Use try-catch blocks to handle exceptions gracefully.
- Monitor application performance and resource usage to identify bottlenecks.
- **Front-end debugging:** Use browser developer tools and check `console.log` outputs.
- **Back-end debugging:** View ThingWorx platform logs.
- **JavaScript Debugging Removal:** All `console.log` and testing `layer.msg` statements must be removed from JavaScript files before deployment.

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

- All code modifications must be accompanied by clear and concise documentation.
- Create detailed architecture diagrams to illustrate system components and their interactions.
- Maintain a consistent documentation style throughout the project.
- Create tasks file to record project's progress.
- Important function modifications should be recorded in `project_document/logs/`.
- SQL script changes need to be archived in the respective developer's directory.
- Update README before version release.
- The `ai-docs/` directory should be used for project documentation.
- The project root directory should contain the `ai-docs/architecture/` directory structure.
- **Panorama Hotspot Editor Analysis Reports:** Store detailed analysis reports for the Panorama Hotspot Editor page in `ai-docs/panorama-editor-report.md`. These reports should include file paths, file descriptions, and other relevant information to guide future AI coding conversations.
- **Panorama Hotspot Editor Documentation Update:** The `ai-docs/panorama-editor-report.md` document **must** be updated whenever the Panorama Hotspot Editor code is modified.

## Data Handling

- When working with data, ensure data integrity and consistency.
- Validate input data to prevent errors and security vulnerabilities.
- Handle different data types appropriately.
- When displaying data in the UI, format it clearly and consistently.
- Always handle null or undefined values gracefully.

## ERROR HANDLING

- Implement robust error handling throughout the application.
- Use try-catch blocks to catch potential exceptions.
- Log errors with informative messages.
- Provide user-friendly error messages in the UI.

## Security
- Be aware of potential security vulnerabilities, such as SQL injection and cross-site scripting (XSS).
- Implement appropriate security measures to protect against these vulnerabilities.
- Validate user input to prevent malicious code from being injected into the system.
- **SQL Injection Prevention:** Use parameterized queries.
- **XSS Prevention:** Escape user input data.
- **Permissions Control:** Verify user operation permissions.
- **Data Backup:** Regularly back up important data.

## Naming Conventions

- Follow consistent naming conventions for files, classes, variables, and functions.
- Use kebab-case for file names.

## DataPackageManagement Specific Rules

### Nonconformity Review Formatting
- When displaying nonconformity review details, strictly adhere to the format specified in the provided images or mockups.
- Ensure all relevant fields from the data source are included in the display.
- Design the layout to resemble a traditional paper form for a professional and familiar look.
- Consider using a table-based layout with borders and shading to achieve the desired effect.

### Branch Data Simulation Service
- Create a dedicated service for simulating branch data.
- Ensure all fields in the branch table have valid values.
- Maintain data consistency between the main and branch tables, especially for key fields like model and development phase.
- Provide options for generating different numbers of branch records per main record.
- The branch FZ filed should follow the format "FZ-BINDID-001".

### ThingWorx Specific Rules
- When modifying ThingWorx JavaScript services, adhere to ThingWorx coding standards.
- Use the standard `result` object for returning data from ThingWorx services.
- Handle InfoTables correctly, extracting data as needed.
- Services should be a single function, not composed of multiple functions.
- Throw errors instead of returning them.
- When working with services in ThingWorx, ensure that proper permissions are set for the Entities.
- **ThingWorx Service Template:**
```javascript
/**
 * @definition    GetDataList
 * @description   获取数据列表    wanghq 2025年6月3日14:38:01
 * @implementation    {Script}
 *
 * @param    {STRING}    searchKey    搜索关键字
 * @param    {INTEGER}    pageNum    页码
 * @param    {INTEGER}    pageSize    每页大小
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 参数验证
    if (!pageNum || pageNum < 1) pageNum = 1;
    if (!pageSize || pageSize < 1) pageSize = 20;

    // 构建SQL查询
    var sql = "SELECT * FROM data_table WHERE 1=1";
    if (searchKey && searchKey.trim() !== '') {
        sql += " AND name LIKE '%" + searchKey + "%'";
    }

    // 分页查询 (Oracle 11g ROWNUM方式)
    var startRow = (pageNum - 1) * pageSize + 1;
    var endRow = pageNum * pageSize;
    var pagingSql = "SELECT * FROM (" +
        "SELECT ROWNUM AS rn, t.* FROM (" + sql + ") t " +
        "WHERE ROWNUM <= " + endRow + ") " +
        "WHERE rn >= " + startRow;

    // 执行查询
    var jsonData = Things['Thing.DB.Oracle'].RunQuery({sql: pagingSql}).ToJSON().rows;

    // 获取总数
    var countSql = "SELECT COUNT(*) as total FROM (" + sql + ")";
    var countResult = Things['Thing.DB.Oracle'].RunQuery({sql: countSql}).ToJSON().rows;
    var total = countResult.length > 0 ? countResult[0].total : 0;

    res.success = true;
    res.data = {
        list: jsonData || [],
        total: total,
        pageNum: pageNum,
        pageSize: pageSize
    };
    res.msg = "查询成功";

} catch (error) {
    res.success = false;
    var msg = "GetDataList-查询失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;
```

### ThingWorx Service Development Standards (Additions based on analysis 2025-06-09)

-   **JSDoc Comments:**
    -   Use JSDoc comments to document services, parameters, and return values.
    -   Update time format to Chinese format (e.g., `2025年6月3日14:38:01`).
    -   Include custom attributes like `{"category":"ext"}` where applicable.
-   **Return Value Handling:**
    -   Support multiple return value patterns.
    -   Provide guidance on selecting appropriate return patterns.
    -   Update example code to reflect various return scenarios.
-   **Service-to-Service Calls:**
    -   Use `me.ServiceName` to call other services.
    -   Clearly document parameter types when calling other services.
    -   Provide examples of service-to-service calls.
-   **Data Handling Best Practices:**
    -   Handle null or empty query results gracefully.
    -   Utilize `fieldDefinitions` for data structure definition.
    -   Follow established patterns for processing JSON results.
-   **Common API References:**
    -   Include examples and usage guidelines for common APIs like `dateFormat` and `base64EncodeString`.
    -   Document Oracle sequence usage patterns.
-   **Service Template Examples:**
    -   Provide templates for different service types:
        -   Query services
        -   Operation services
        -   Statistical services

## Version Control
- Always use version control (e.g., Git) to track changes to the codebase.
- Create meaningful commit messages to explain the purpose of each change.
- Use branches to isolate new features or bug fixes.
- Follow a consistent branching strategy.

## Testing
- Write unit tests to verify the correctness of individual components.
- Perform integration tests to ensure that different parts of the system work together properly.
- Conduct user acceptance testing (UAT) to validate that the system meets the needs of the users.
- **Unit Test Example (JavaScript):**
```javascript
function testFunction() {
    // 测试数据准备
    var testData = {name: '测试用户', age: 25};

    // 执行被测试函数
    var result = processUserData(testData);

    // 断言验证
    console.assert(result.success === true, '函数应该返回成功状态');
    console.assert(result.data.name === '测试用户', '用户名应该正确处理');
}
```

## Cursor Rules
- The project now includes cursor rules located in `.cursor/rules/`.
- The rules consist of two main files: `data-package-management-architecture.mdc` and `development-best-practices.mdc`.
- `data-package-management-architecture.mdc`: Contains system architecture navigation and business process descriptions.
- `development-best-practices.mdc`: Contains development best practices and coding standards, including ES5 syntax, ThingWorx service development standards, and Oracle database operation specifications.

## Panorama Hotspot Editor Specific Rules

### Project Overview
- **Project Name:** Panorama Hotspot Editor
- **Tech Stack:** Spring Boot 2.7.18 + JDK 1.8 + Layui 2.10.3 + jQuery 3.x + Oracle 11g
- **Project Type:** Web application
- **Development Status:** Production-ready
- **Last Updated:** 2025-06-08

### File Structure
- **Frontend:**
  - `FileHandle/src/main/webapp/panorama-editor.html`: Main HTML page (272 lines)
  - `FileHandle/src/main/webapp/panorama/css/panorama-editor.css`: CSS Styles (810 lines)
  - `FileHandle/src/main/webapp/panorama/js/panorama-editor.js`: Core JavaScript logic (1334 lines)
  - `FileHandle/src/main/webapp/panorama/js/pano2vr-hotspot-locator.js`: Hotspot locator script (112 lines)
- **Backend:**
  - `FileHandle/src/main/java/com/cirpoint/controller/PanoramaController.java`: REST API controller (401 lines)
  - `FileHandle/src/main/java/com/cirpoint/service/PanoramaService.java`: Business logic service (1057 lines)
  - `FileHandle/src/main/java/com/cirpoint/util/Pano2VRHotspotInjector.java`: Script injection utility (224 lines)
- **Database:**
  - `DataPackageManagement/sql/panorama_tables.sql`: Database table definitions (190 lines)

### API Endpoints
- `POST /panorama/task/create`: Create task
- `GET /panorama/task/list`: Get task list
- `GET /panorama/task/{taskId}`: Get task details
- `GET /panorama/check/hotspots`: Check hotspot data
- `POST /panorama/clear/data`: Clear task data
- `POST /panorama/upload/zip`: Upload panorama ZIP
- `POST /panorama/upload/excel`: Upload device Excel
- `GET /panorama/device/list`: Get device list
- `GET /panorama/device/export`: Export device Excel
- `GET /panorama/hotspot/list`: Get hotspot list
- `POST /panorama/hotspot/update`: Update hotspot information
- `POST /panorama/hotspot/locate`: Hotspot location data
- `POST /panorama/export`: Export panorama
- `GET /panorama/preview/path`: Get preview path

### System Architecture
```
Frontend (Layui + jQuery)
↓ HTTP/AJAX
Controller (Spring Boot REST API)
↓ Method Call
Service Layer (Business Logic)
↓ Branching
File System | Database | Utilities
```

### Core Technologies
- **Frontend:** Layui 2.10.3, jQuery 3.x, CSS3, HTML5, jQuery FileDownload plugin
- **Backend:** Spring Boot 2.7.18, JDK 1.8, Hutool, Dom4j, Apache Commons
- **Database:** Oracle 11g

### Development Setup
- **JDK:** 1.8 or higher
- **IDE:** Eclipse/IntelliJ IDEA
- **Build Tool:** Maven
- **Database:** Oracle 11g or higher
- **Web Server:** Tomcat 8.5 or higher

### Database Initialization
1.  Connect to Oracle: `sqlplus username/password@database`
2.  Run script: `@DataPackageManagement/sql/panorama_tables.sql`

### Configuration
- **application.properties:**
```properties
spring.datasource.url=*************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
```
- **File Upload Path:** Defined in `ApplicationConfig.java`

### Directory Structure
```
fileUploadPath/
└── panorama/
    └── {taskId}/
        ├── panorama_timestamp.zip
        └── extracted/
            ├── *.html
            ├── *.xml
            └── images/
```

### Debugging
- **Frontend:**
```javascript
var DEBUG_MODE = true;
if (DEBUG_MODE) {
    console.log('Debug Info:', data);
}
```
- **Backend:**
```java
@Slf4j
public class PanoramaService {
    public void someMethod() {
        log.info("Method started");
        log.debug("Data: {}", data);
        log.error("Error", exception);
    }
}
```

### Deployment
- **Files:**
  - `FileHandle.war`
  - `DataPackageManagement/sql/panorama_tables.sql`
  - `application-prod.properties`
  - `logback-spring.xml`
- **application-prod.properties (Example):**
```properties
server.port=8080
spring.datasource.url=***********************************
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
file.upload.path=/opt/panorama/uploads
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
logging.level.com.cirpoint=INFO
logging.file.path=/opt/panorama/logs
```

### Troubleshooting
- **File Upload Issues:** Check file size limits, ZIP format, directory permissions, and backend logs.
- **Hotspot Location Issues:** Check iframe loading, script injection, postMessage communication, and PAN/TILT data.
- **Table Display Issues:** Check database connection, API data format, Layui initialization, and JavaScript errors.

### Version History
- **v1.0.0 (2025-06-08):** Updated project documentation to reflect latest code structure and moved node information to the hot spot editor table header.

### Panorama Hotspot Editor Detailed Summary Report

- This section provides a detailed summary of the Panorama Hotspot Editor, generated on 2025-06-08, based on a review of the code files and the document `项目开发总结_全景图热点编辑系统.md`. This summary is intended to guide future AI coding conversations.
- **Report Updated By:** wanghq
- **Report Generation Time:** `[2025-06-08 10:16:04]`
- **Documentation Update Trigger:** The `ai-docs/panorama-editor-report.md` document **must** be updated whenever the Panorama Hotspot Editor code is modified.

#### 1. System Overview

- The Panorama Hotspot Editor is a full-featured web application designed to provide an efficient and intuitive platform for managing panorama projects, editing hotspot information in images, and associating it with external device data. The system supports a complete workflow from task creation to file uploading, hotspot editing, real-time preview, hotspot location, and final result export.
- The system adopts a modern design concept, providing a friendly user interface and smooth operation experience, especially in task guidance, data integrity checking, and hotspot location.
- **Recent Updates (2025-06-09):**
    -   Enhanced to support multi-node panoramas.
    -   Implemented direct hotspot editing via clicks on the panorama.
    -   Improved iframe communication mechanisms.
    -   Optimized hotspot location script injection.

#### 2. Core Technology Stack
-   **Database:** Oracle 11g
-   **Panorama Engine:** Pano2VR (integrated via iframe)
-   **File Processing:** Apache Commons, Hutool tool libraries
-   **Frontend Enhancement:** jQuery FileDownload plugin

#### 3. Associated File List and Analysis

##### 3.1. Frontend Files

| File Path                                                                 | File Type | Core Responsibilities                                                                                                                                                                                                                                                           |
| ------------------------------------------------------------------------ | -------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `FileHandle/src/main/webapp/panorama-editor.html`                        | HTML     | **System Main Framework**. Adopts a responsive layout design, including a top navigation bar (task selection, creation, export), left and right column structure (task information, hotspot editing table, panorama preview), task guidance mask, and various dialog boxes (create task, edit hotspot). |
| `FileHandle/src/main/webapp/panorama/css/panorama-editor.css`            | CSS      | **Visual Styles and Interaction Effects**. Implements a modern interface design, including gradient backgrounds, dark theme adaptation, draggable separator styles, task selection guide mask animation, deep customization of Layui components, and complete responsive layout support. |
| `FileHandle/src/main/webapp/panorama/js/panorama-editor.js`              | JS       | **Page Main Logic Controller** (1484 lines). Responsible for complete front-end interaction logic, including Layui component initialization, task management processes, file upload handling, hotspot editing functions, preview management, node switching, data integrity checking, and comprehensive communication with the back-end API. |
| `FileHandle/src/main/webapp/panorama/js/pano2vr-hotspot-locator.js`      | JS       | **Hotspot Locator Script**. A dedicated script for the hotspot location function, dynamically injected into the HTML generated by Pano2VR. It receives `postMessage` instructions from the main page and calls the Pano2VR API to achieve precise hotspot location.                                  |
| `FileHandle/src/main/webapp/static/lib/jquery/jquery.fileDownload.js`    | JS       | **File Download Enhancement Plugin**. Provides a more reliable file download experience than native `window.open`, supporting file stream processing, download progress feedback, error handling, and other advanced features.                                                                  |

##### 3.2. Backend Files

| File Path                                                              | File Type | Core Responsibilities                                                                                                                                                                                                                                                          |
| ------------------------------------------------------------------- | -------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `FileHandle/src/main/java/com/cirpoint/controller/PanoramaController.java` | Java     | **RESTful API Controller** (402 lines). Defines a complete API interface specification, including 15 core interfaces for task management (creation, query, details), file uploading (ZIP, Excel), hotspot operations (query, update, location), data checking and cleaning, preview path acquisition, and export functions. |
| `FileHandle/src/main/java/com/cirpoint/service/PanoramaService.java`       | Java     | **Core Business Logic Layer**. Implements the specific logic for all business functions, including database CRUD operations, file processing (ZIP decompression/packaging, Excel parsing/generation), script injection management, preview path processing, and complex business process control.                                  |
| `FileHandle/src/main/java/com/cirpoint/util/Pano2VRHotspotInjector.java`     | Java     | **Script Injection/Cleaning Tool Class**. Specifically responsible for automatically injecting the hotspot location script into HTML files during ZIP package processing, and cleaning the script during export to ensure the purity of the delivered files and the continuity of online editing functions.                                 |

##### 3.3. Database Table Structure

| Table Name          | Core Responsibilities                                        | Key Fields                                    |
| ------------------- | --------------------------------------------------------------- | ------------------------------------------ |
| `PANORAMA_TASK`     | Stores basic task information and status management.                    | TASK_ID, TASK_NAME, MODEL_ID, STATUS, etc.     |
| `PANORAMA_HOTSPOT`  | Stores detailed hotspot information and editing status for each task.           | HOTSPOT_ID, TASK_ID, PAN, TILT, EDITED, etc.   |
| `PANORAMA_DEVICE`   | Stores single-machine/device information associated with the task.                   | DEVICE_ID, TASK_ID, DEVICE_NAME, etc.          |

##### 3.3.1 SQL Creation Statements

```sql
-- PANORAMA_TASK table creation
CREATE TABLE PANORAMA_TASK (
    TASK_ID VARCHAR2(50) NOT NULL,
    TASK_NAME VARCHAR2(200),
    MODEL_ID VARCHAR2(50),
    STATUS VARCHAR2(50),
    CREATE_TIME DATE,
    UPDATE_TIME DATE,
    CREATE_USER VARCHAR2(50),
    UPDATE_USER VARCHAR2(50),
    DESCRIPTION VARCHAR2(500),
    EXT1 VARCHAR2(200),
    EXT2 VARCHAR2(200),
    CONSTRAINT PK_PANORAMA_TASK PRIMARY KEY (TASK_ID)
);

COMMENT ON TABLE PANORAMA_TASK IS '全景图任务表';
COMMENT ON COLUMN PANORAMA_TASK.TASK_ID IS '任务ID';
COMMENT ON COLUMN PANORAMA_TASK.TASK_NAME IS '任务名称';
COMMENT ON COLUMN PANORAMA_TASK.MODEL_ID IS '模型ID';
COMMENT ON COLUMN PANORAMA_TASK.STATUS IS '任务状态';
COMMENT ON COLUMN PANORAMA_TASK.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN PANORAMA_TASK.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN PANORAMA_TASK.CREATE_USER IS '创建用户';
COMMENT ON COLUMN PANORAMA_TASK.UPDATE_USER IS '更新用户';
COMMENT ON COLUMN PANORAMA_TASK.DESCRIPTION IS '描述';
COMMENT ON COLUMN PANORAMA_TASK.EXT1 IS '扩展字段1';
COMMENT ON COLUMN PANORAMA_TASK.EXT2 IS '扩展字段 2';

-- PANORAMA_HOTSPOT table creation
CREATE TABLE PANORAMA_HOTSPOT (
    HOTSPOT_ID VARCHAR2(50) NOT NULL,
    TASK_ID VARCHAR2(50) NOT NULL,
    DEVICE_ID VARCHAR2(50),
    HOTSPOT_NAME VARCHAR2(200),
    PAN NUMBER,
    TILT NUMBER,
    DESCRIPTION VARCHAR2(500),
    URL VARCHAR2(500),
    ICON VARCHAR2(200),
    CREATE_TIME DATE,
    UPDATE_TIME DATE,
    CREATE_USER VARCHAR2(50),
    UPDATE_USER VARCHAR2(50),
    EDITED NUMBER(1),
    EXT1 VARCHAR2(200),
    EXT2 VARCHAR2(200),
    EXT3 VARCHAR2(200),
    CONSTRAINT PK_PANORAMA_HOTSPOT PRIMARY KEY (HOTSPOT_ID)
);

COMMENT ON TABLE PANORAMA_HOTSPOT IS '全景图热点表';
COMMENT ON COLUMN PANORAMA_HOTSPOT.HOTSPOT_ID IS '热点ID';
COMMENT ON COLUMN PANORAMA_HOTSPOT.TASK_ID IS '任务ID';
COMMENT ON COLUMN PANORAMA_HOTSPOT.DEVICE_ID IS '设备ID';
COMMENT ON COLUMN PANORAMA_HOTSPOT.HOTSPOT_NAME IS '热点名称';
COMMENT ON COLUMN PANORAMA_HOTSPOT.PAN IS 'PAN坐标';
COMMENT ON COLUMN PANORAMA_HOTSPOT.TILT IS 'TILT坐标';
COMMENT ON COLUMN PANORAMA_HOTSPOT.DESCRIPTION IS '描述';
COMMENT ON COLUMN PANORAMA_HOTSPOT.URL IS 'URL';
COMMENT ON COLUMN PANORAMA_HOTSPOT.ICON IS '图标';
COMMENT ON COLUMN PANORAMA_HOTSPOT.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN PANORAMA_HOTSPOT.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN PANORAMA_HOTSPOT.CREATE_USER IS '创建用户';
COMMENT ON COLUMN PANORAMA_HOTSPOT.UPDATE_USER IS '更新用户';
COMMENT ON COLUMN PANORAMA_HOTSPOT.EDITED IS '是否已编辑';
COMMENT ON COLUMN PANORAMA_HOTSPOT.EXT1 IS '扩展字段1';
COMMENT ON COLUMN PANORAMA_HOTSPOT.EXT2 IS '扩展字段 2';
COMMENT ON COLUMN PANORAMA_HOTSPOT.EXT3 IS '扩展字段3';

-- PANORAMA_DEVICE table creation
CREATE TABLE PANORAMA_DEVICE (
    DEVICE_ID VARCHAR2(50) NOT NULL,
    TASK_ID VARCHAR2(50) NOT NULL,
    DEVICE_NAME VARCHAR2(200),
    DEVICE_TYPE VARCHAR2(50),
    IP_ADDRESS VARCHAR2(50),
    SERIAL_NUMBER VARCHAR2(100),
    CREATE_TIME DATE,
    UPDATE_TIME DATE,
    CREATE_USER VARCHAR2(50),
    UPDATE_USER VARCHAR2(50),
    CONSTRAINT PK_PANORAMA_DEVICE PRIMARY KEY (DEVICE_ID)
);

COMMENT ON TABLE PANORAMA_DEVICE IS '全景图设备表';
COMMENT ON COLUMN PANORAMA_DEVICE.DEVICE_ID IS '设备ID';
COMMENT ON COLUMN PANORAMA_DEVICE.TASK_ID IS '任务ID';
COMMENT ON COLUMN PANORAMA_DEVICE.DEVICE_NAME IS '设备名称';
COMMENT ON COLUMN PANORAMA_DEVICE.DEVICE_TYPE IS '设备类型';
COMMENT ON COLUMN PANORAMA_DEVICE.IP_ADDRESS IS 'IP地址';
COMMENT ON COLUMN PANORAMA_DEVICE.SERIAL_NUMBER IS '序列号';
COMMENT ON COLUMN PANORAMA_DEVICE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN PANORAMA_DEVICE.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN PANORAMA_DEVICE.CREATE_USER IS '创建用户';
COMMENT ON COLUMN PANORAMA_DEVICE.UPDATE_USER IS '更新用户';
```

##### 3.3.2 Index Suggestions

```sql
-- Index for PANORAMA_HOTSPOT.TASK_ID
CREATE INDEX IDX_PANORAMA_HOTSPOT_TASKID ON PANORAMA_HOTSPOT (TASK_ID);

-- Index for PANORAMA_DEVICE.TASK_ID
CREATE INDEX IDX_PANORAMA_DEVICE_TASKID ON PANORAMA_DEVICE (TASK_ID);

-- Index for PANORAMA_TASK.MODEL_ID
CREATE INDEX IDX_PANORAMA_TASK_MODELID ON PANORAMA_TASK (MODEL_ID);

-- Composite index for PANORAMA_HOTSPOT (PAN, TILT)
CREATE INDEX IDX_PANORAMA_HOTSPOT_PANTILT ON PANORAMA_HOTSPOT (PAN, TILT);
```

##### 3.3.3 Sequence Creation

```sql
-- Sequence for generating unique IDs
CREATE SEQUENCE SEQ_COMMON_ID
    MINVALUE 1
    MAXVALUE 999999999999999999999999999
    START WITH 1
    INCREMENT BY 1
    NOCACHE;
```

---

#### 4. Core Workflows

##### 4.1. Page Initialization and Task Guidance Process
1.  **Page Loading**: User visits `panorama-editor.html`, and the page uses a fixed header layout with the main area height set to `calc(100vh - 60px)`.
2.  **JS Initialization**: `panorama-editor.js` executes three core initialization functions:
    -   `initPage()`: Initializes the hotspot table, file upload component, drag functionality, and moves current node information (task information) to the hotspot editor table header.
    -   `bindEvents()`: Binds all UI event listeners
    -   `loadTaskList()`: Asynchronously loads task list data
3.  **Guidance Mask Display**: `showTaskSelectionMask()` displays a semi-transparent guidance mask with a fade-in animation, guiding the user to select a task.
4.  **Task Selection Process**:
    -   Users can select an existing task via the top dropdown box
    -   Or click the "Create Task" button to create a new task
    -   The system supports a task switching confirmation mechanism to prevent data loss
5.  **Mask Removal**: After a task is selected, `hideTaskSelectionMask()` executes a fade-out animation and enables all function buttons.

##### 4.2. Data Integrity and Cleaning Process
1.  **Pre-Upload Check**: User uploads file, system calls `GET /panorama/check/hotspots` to check for existing data.
2.  **Confirmation Dialog**: If existing data detected, `layer.confirm` dialog informs user of operation consequences.
3.  **Data Cleaning**: User confirms, `POST /panorama/clear/data` cleans related data table records.
4.  **Execute Upload**: Cleaning complete, upload process continues, ensuring data consistency.

##### 4.3. Hotspot Location Workflow (System Core Technical Difficulty)
This is system's most complex function, involving front-end collaboration, iframe communication, third-party library integration.

###### Preparation Stage (ZIP upload):
1.  **File Reception**: Backend `PanoramaController.uploadPanoramaZip()` receives ZIP file
2.  **Decompression Processing**: `PanoramaService` 将ZIP包解压到指定目录
3.  **Script Injection**: 调用 `Pano2VRHotspotInjector.processDirectory()`:
    - 遍历所有 `.html` 文件
    - 在每个HTML的 `</body>` 前注入 `<script src=".../pano2vr-hotspot-locator.js"></script>`
    - 注入后的HTML具备接收定位指令的能力

###### Execution Stage (User clicks Locate):
1.  **Event Trigger**: User clicks "Locate" in hotspot table
2.  **Frontend Processing**: `panorama-editor.js` 的 `locateHotspot(hotspotId)` 函数被调用
3.  **Backend Query**: 向 `POST /panorama/hotspot/locate` 发送请求获取热点的 `PAN` 和 `TILT` 坐标
4.  **Iframe Communication**: 通过 `panoramaFrame.contentWindow.postMessage()` 向预览窗口发送定位指令
5.  **Script Response**: 注入的 `pano2vr-hotspot-locator.js` 监听消息并调用 `pano.moveTo(pan, tilt)` API
6.  **View Movement**: 全景图视角平滑移动到指定坐标位置

###### Cleaning Stage (Export):
1.  **Export Trigger**: User clicks export button, triggers `POST /panorama/export`
2.  **Script Clean**: 调用 `Pano2VRHotspotInjector.cleanDirectory()` 使用正则表达式移除注入的脚本标签
3.  **File Package**: 打包清理后的纯净文件返还给用户
4.  **Re-inject**: 导出完成后立即重新注入脚本，保证在线编辑功能不受影响

##### 4.4. Node Switching and Status Management
1.  **Current Node Display**: System will display the current panorama node information being edited
2.  **Node Switch Detect**: Through `handleNodeSwitch()` handle node changes
3.  **Table Refresh**: Hotspot table data automatically refreshed on node switch
4.  **Status Sync**: Ensure UI status is consistent with backend data

#### 5. User Interface Features

##### 5.1. Task Guidance System
- **Guidance Mask**: Gradient animation effect
- **Status Hints**: Clear task status display and operation guidance
- **Anti-Mist